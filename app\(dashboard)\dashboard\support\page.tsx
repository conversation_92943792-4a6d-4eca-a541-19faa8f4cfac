"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { Clock, HelpCircle, Mail, MessageCircle, Phone } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function Support() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [selectedTopic, setSelectedTopic] = useState("");
   const [message, setMessage] = useState("");

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const supportTopics = [
      "Account Issues",
      "Card Problems",
      "Transaction Disputes",
      "Online Banking",
      "Mobile App",
      "Loans & Credit",
      "Investment Services",
      "Other",
   ];

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      // Handle support request submission
      alert("Support request submitted successfully!");
      setMessage("");
      setSelectedTopic("");
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb items={[{ label: "Support" }]} />

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-4xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-4">
                     <HelpCircle className="w-8 h-8 text-accent" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     How can we help you?
                  </h1>
                  <p className="text-muted-foreground">
                     Our support team is available 24/7 to assist you with any
                     questions or concerns.
                  </p>
               </div>

               <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Contact Options */}
                  <div className="space-y-6">
                     <div className="bank-card p-6">
                        <h2 className="text-xl font-semibold text-foreground mb-4">
                           Contact Us
                        </h2>
                        <div className="space-y-4">
                           <div className="flex items-center gap-4 p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors">
                              <div className="p-2 bg-primary/10 rounded-lg">
                                 <Phone className="w-5 h-5 text-primary" />
                              </div>
                              <div>
                                 <p className="font-medium text-foreground">
                                    Phone Support
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    +****************
                                 </p>
                                 <p className="text-xs text-success">
                                    Available 24/7
                                 </p>
                              </div>
                           </div>

                           <div className="flex items-center gap-4 p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors">
                              <div className="p-2 bg-success/10 rounded-lg">
                                 <Mail className="w-5 h-5 text-success" />
                              </div>
                              <div>
                                 <p className="font-medium text-foreground">
                                    Email Support
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    <EMAIL>
                                 </p>
                                 <p className="text-xs text-muted-foreground">
                                    Response within 2 hours
                                 </p>
                              </div>
                           </div>

                           <div className="flex items-center gap-4 p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors">
                              <div className="p-2 bg-accent/10 rounded-lg">
                                 <MessageCircle className="w-5 h-5 text-accent" />
                              </div>
                              <div>
                                 <p className="font-medium text-foreground">
                                    Live Chat
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    Chat with our agents
                                 </p>
                                 <p className="text-xs text-success">
                                    Online now
                                 </p>
                              </div>
                           </div>
                        </div>
                     </div>

                     <div className="bank-card p-6">
                        <h2 className="text-xl font-semibold text-foreground mb-4">
                           Business Hours
                        </h2>
                        <div className="space-y-3">
                           <div className="flex items-center gap-3">
                              <Clock className="w-4 h-4 text-muted-foreground" />
                              <div>
                                 <p className="font-medium text-foreground">
                                    Phone & Chat Support
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    24/7 - Always available
                                 </p>
                              </div>
                           </div>
                           <div className="flex items-center gap-3">
                              <Clock className="w-4 h-4 text-muted-foreground" />
                              <div>
                                 <p className="font-medium text-foreground">
                                    Branch Locations
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    Mon-Fri: 9AM-5PM, Sat: 9AM-2PM
                                 </p>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>

                  {/* Support Request Form */}
                  <div className="bank-card p-6">
                     <h2 className="text-xl font-semibold text-foreground mb-4">
                        Send us a message
                     </h2>
                     <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                           <label className="text-sm font-medium text-foreground mb-2 block">
                              Topic
                           </label>
                           <select
                              value={selectedTopic}
                              onChange={(e) => setSelectedTopic(e.target.value)}
                              className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                              required
                           >
                              <option value="">Select a topic</option>
                              {supportTopics.map((topic) => (
                                 <option key={topic} value={topic}>
                                    {topic}
                                 </option>
                              ))}
                           </select>
                        </div>

                        <div>
                           <label className="text-sm font-medium text-foreground mb-2 block">
                              Message
                           </label>
                           <textarea
                              value={message}
                              onChange={(e) => setMessage(e.target.value)}
                              placeholder="Describe your issue or question..."
                              className="w-full p-3 border border-border rounded-lg bg-background text-foreground min-h-[120px] resize-none"
                              required
                           />
                        </div>

                        <Button
                           type="submit"
                           variant="premium"
                           className="w-full"
                           disabled={!selectedTopic || !message.trim()}
                        >
                           Send Message
                        </Button>
                     </form>
                  </div>
               </div>
            </motion.div>
         </div>
      </div>
   );
}
