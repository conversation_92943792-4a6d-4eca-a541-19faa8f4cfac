"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { CreditCard, Shield, Star } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function ApplyForCard() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [selectedCard, setSelectedCard] = useState("");
   const [formData, setFormData] = useState({
      cardType: "virtual", // virtual or physical
      currency: "USD",
      dailyLimit: "",
      billingAddress: "",
      billingCity: "",
      billingState: "",
      billingZip: "",
      annualIncome: "",
      employment: "",
      housingStatus: "",
   });

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const cardOptions = [
      {
         id: "standard",
         name: "Standard Virtual Card",
         type: "Virtual Card",
         issuanceFee: "$5.00",
         cashback: "1%",
         features: [
            "Instant issuance",
            "1% cashback on all purchases",
            "Basic fraud protection",
            "Mobile app management",
         ],
         icon: CreditCard,
         color: "bg-gradient-to-br from-slate-600 to-slate-800",
      },
      {
         id: "gold",
         name: "Gold Virtual Card",
         type: "Virtual Card",
         issuanceFee: "$15.00",
         cashback: "2%",
         features: [
            "Instant issuance",
            "2% cashback on all purchases",
            "Enhanced security",
            "Priority support",
         ],
         icon: Star,
         color: "bg-gradient-to-br from-yellow-500 to-yellow-700",
      },
      {
         id: "platinum",
         name: "Platinum Virtual Card",
         type: "Virtual Card",
         issuanceFee: "$25.00",
         cashback: "3%",
         features: [
            "Instant issuance",
            "3% cashback on all purchases",
            "Premium security features",
            "Concierge service",
         ],
         icon: Shield,
         color: "bg-gradient-to-br from-gray-400 to-gray-600",
      },
      {
         id: "black",
         name: "Black Virtual Card",
         type: "Virtual Card",
         issuanceFee: "$50.00",
         cashback: "5%",
         features: [
            "Instant issuance",
            "5% cashback on all purchases",
            "Maximum security",
            "VIP support",
         ],
         icon: Shield,
         color: "bg-gradient-to-br from-gray-900 to-black",
      },
   ];

   const currencies = [
      { code: "USD", name: "US Dollar", symbol: "$" },
      { code: "EUR", name: "Euro", symbol: "€" },
      { code: "GBP", name: "British Pound", symbol: "£" },
      { code: "CAD", name: "Canadian Dollar", symbol: "C$" },
      { code: "AUD", name: "Australian Dollar", symbol: "A$" },
   ];

   const handleInputChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
   ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (!selectedCard) return;

      const card = cardOptions.find((c) => c.id === selectedCard);
      alert(
         `Application for ${card?.name} submitted successfully! You'll receive a decision within 7-10 business days.`
      );

      setSelectedCard("");
      setFormData({
         cardType: "virtual",
         currency: "USD",
         dailyLimit: "",
         billingAddress: "",
         billingCity: "",
         billingState: "",
         billingZip: "",
         annualIncome: "",
         employment: "",
         housingStatus: "",
      });
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb
            items={[
               { label: "Cards", href: "/dashboard/cards" },
               { label: "Apply" },
            ]}
         />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-4xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                     <CreditCard className="w-8 h-8 text-primary" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Apply for a Credit Card
                  </h1>
                  <p className="text-muted-foreground">
                     Choose the perfect card for your lifestyle and start
                     earning rewards today.
                  </p>
               </div>

               {/* Card Selection */}
               <div className="mb-8">
                  <h2 className="text-xl font-semibold text-foreground mb-6">
                     Choose Your Card
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                     {cardOptions.map((card) => {
                        const IconComponent = card.icon;
                        return (
                           <label
                              key={card.id}
                              className={`cursor-pointer transition-all ${
                                 selectedCard === card.id
                                    ? "ring-2 ring-primary"
                                    : "hover:shadow-lg"
                              }`}
                           >
                              <input
                                 type="radio"
                                 name="cardType"
                                 value={card.id}
                                 checked={selectedCard === card.id}
                                 onChange={(e) =>
                                    setSelectedCard(e.target.value)
                                 }
                                 className="sr-only"
                              />
                              <div className="bank-card p-6 h-full">
                                 {/* Card Visual */}
                                 <div
                                    className={`${card.color} rounded-lg p-4 mb-4 text-white relative overflow-hidden`}
                                 >
                                    <div className="flex justify-between items-start mb-8">
                                       <IconComponent className="w-8 h-8" />
                                       <div className="text-right">
                                          <p className="text-xs opacity-80">
                                             Issuance Fee
                                          </p>
                                          <p className="font-bold">
                                             {card.issuanceFee}
                                          </p>
                                       </div>
                                    </div>
                                    <div>
                                       <p className="text-xs opacity-80 mb-1">
                                          Cashback Rate
                                       </p>
                                       <p className="text-2xl font-bold">
                                          {card.cashback}
                                       </p>
                                    </div>
                                    <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                                 </div>

                                 {/* Card Details */}
                                 <div>
                                    <h3 className="font-semibold text-foreground mb-1">
                                       {card.name}
                                    </h3>
                                    <p className="text-sm text-muted-foreground mb-4">
                                       {card.type}
                                    </p>

                                    <ul className="space-y-2">
                                       {card.features.map((feature, index) => (
                                          <li
                                             key={index}
                                             className="text-sm text-muted-foreground flex items-center gap-2"
                                          >
                                             <div className="w-1.5 h-1.5 bg-success rounded-full"></div>
                                             {feature}
                                          </li>
                                       ))}
                                    </ul>
                                 </div>
                              </div>
                           </label>
                        );
                     })}
                  </div>
               </div>

               {/* Application Form */}
               {selectedCard && (
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.4 }}
                     className="bank-card p-6"
                  >
                     <h2 className="text-xl font-semibold text-foreground mb-6">
                        Application Information
                     </h2>
                     <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Card Configuration */}
                        <div className="space-y-4">
                           <h3 className="text-lg font-semibold text-foreground">
                              Card Configuration
                           </h3>
                           <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                 <label className="text-sm font-medium text-foreground mb-2 block">
                                    Card Type
                                 </label>
                                 <select
                                    name="cardType"
                                    value={formData.cardType}
                                    onChange={handleInputChange}
                                    className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                    required
                                 >
                                    <option value="virtual">
                                       Virtual Card
                                    </option>
                                    <option value="physical">
                                       Physical Card
                                    </option>
                                 </select>
                              </div>

                              <div>
                                 <label className="text-sm font-medium text-foreground mb-2 block">
                                    Currency
                                 </label>
                                 <select
                                    name="currency"
                                    value={formData.currency}
                                    onChange={handleInputChange}
                                    className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                    required
                                 >
                                    {currencies.map((currency) => (
                                       <option
                                          key={currency.code}
                                          value={currency.code}
                                       >
                                          {currency.code} - {currency.name}
                                       </option>
                                    ))}
                                 </select>
                              </div>

                              <Input
                                 label="Daily Spending Limit"
                                 name="dailyLimit"
                                 type="number"
                                 value={formData.dailyLimit}
                                 onChange={handleInputChange}
                                 placeholder="e.g., 1000"
                                 required
                              />
                           </div>
                        </div>

                        {/* Billing Information */}
                        <div className="space-y-4">
                           <h3 className="text-lg font-semibold text-foreground">
                              Billing Information
                           </h3>
                           <div className="space-y-4">
                              <Input
                                 label="Billing Address"
                                 name="billingAddress"
                                 value={formData.billingAddress}
                                 onChange={handleInputChange}
                                 placeholder="Enter your billing address"
                                 required
                              />
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                 <Input
                                    label="City"
                                    name="billingCity"
                                    value={formData.billingCity}
                                    onChange={handleInputChange}
                                    placeholder="City"
                                    required
                                 />
                                 <Input
                                    label="State"
                                    name="billingState"
                                    value={formData.billingState}
                                    onChange={handleInputChange}
                                    placeholder="State"
                                    required
                                 />
                                 <Input
                                    label="ZIP Code"
                                    name="billingZip"
                                    value={formData.billingZip}
                                    onChange={handleInputChange}
                                    placeholder="ZIP Code"
                                    required
                                 />
                              </div>
                           </div>
                        </div>

                        {/* Financial Information */}
                        <div className="space-y-4">
                           <h3 className="text-lg font-semibold text-foreground">
                              Financial Information
                           </h3>
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-6" />
                           <Input
                              label="Annual Income"
                              name="annualIncome"
                              type="number"
                              value={formData.annualIncome}
                              onChange={handleInputChange}
                              placeholder="Enter your annual income"
                              required
                           />

                           <div>
                              <label className="text-sm font-medium text-foreground mb-2 block">
                                 Employment Status
                              </label>
                              <select
                                 name="employment"
                                 value={formData.employment}
                                 onChange={handleInputChange}
                                 className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                 required
                              >
                                 <option value="">
                                    Select employment status
                                 </option>
                                 <option value="employed">Employed</option>
                                 <option value="self-employed">
                                    Self-Employed
                                 </option>
                                 <option value="student">Student</option>
                                 <option value="retired">Retired</option>
                                 <option value="unemployed">Unemployed</option>
                              </select>
                           </div>

                           <div>
                              <label className="text-sm font-medium text-foreground mb-2 block">
                                 Housing Status
                              </label>
                              <select
                                 name="housingStatus"
                                 value={formData.housingStatus}
                                 onChange={handleInputChange}
                                 className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                 required
                              >
                                 <option value="">Select housing status</option>
                                 <option value="own">Own</option>
                                 <option value="rent">Rent</option>
                                 <option value="mortgage">Mortgage</option>
                                 <option value="other">Other</option>
                              </select>
                           </div>
                        </div>

                        {/* Card Issuance Fee Information */}
                        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                           <h4 className="font-semibold text-foreground mb-3">
                              Card Issuance Fee
                           </h4>
                           <p className="text-sm text-muted-foreground mb-3">
                              There is a one-time issuance fee for your new
                              virtual cards:
                           </p>
                           <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                              <div className="text-center p-2 bg-background rounded">
                                 <p className="font-medium text-foreground">
                                    Standard
                                 </p>
                                 <p className="text-sm text-success">$5.00</p>
                              </div>
                              <div className="text-center p-2 bg-background rounded">
                                 <p className="font-medium text-foreground">
                                    Gold
                                 </p>
                                 <p className="text-sm text-warning">$15.00</p>
                              </div>
                              <div className="text-center p-2 bg-background rounded">
                                 <p className="font-medium text-foreground">
                                    Platinum
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    $25.00
                                 </p>
                              </div>
                              <div className="text-center p-2 bg-background rounded">
                                 <p className="font-medium text-foreground">
                                    Black
                                 </p>
                                 <p className="text-sm text-foreground">
                                    $50.00
                                 </p>
                              </div>
                           </div>
                           <p className="text-xs text-muted-foreground">
                              The fee will be charged to your account
                              immediately upon approval.
                           </p>
                        </div>

                        <div className="bg-muted/30 rounded-lg p-4">
                           <p className="text-sm text-muted-foreground">
                              By submitting this application, you authorize
                              Paramount Bank to check your credit report and
                              verify the information provided. Your application
                              will be reviewed and you&apos;ll receive a
                              decision within 7-10 business days.
                           </p>
                        </div>

                        <Button
                           type="submit"
                           variant="premium"
                           size="lg"
                           className="w-full"
                           disabled={
                              !formData.annualIncome ||
                              !formData.employment ||
                              !formData.housingStatus
                           }
                        >
                           Submit Application
                        </Button>
                     </form>
                  </motion.div>
               )}

               {/* FAQ Section */}
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  className="bank-card p-8 mt-8"
               >
                  <div className="text-center mb-8">
                     <h2 className="text-2xl font-bold text-foreground mb-2">
                        Frequently Asked Questions
                     </h2>
                     <p className="text-muted-foreground">
                        Common questions about virtual card applications
                     </p>
                  </div>

                  <div className="space-y-4">
                     {[
                        {
                           question:
                              "How long does the application process take?",
                           answer:
                              "Virtual card applications are typically processed within minutes. You'll receive instant approval for most applications, and your virtual card will be available immediately.",
                        },
                        {
                           question: "What documents do I need to apply?",
                           answer:
                              "For virtual cards, you typically only need a valid government-issued ID and proof of income. Additional documentation may be required for higher-tier cards.",
                        },
                        {
                           question:
                              "Can I change my daily spending limit later?",
                           answer:
                              "Yes, you can adjust your daily spending limit anytime through your online banking portal or mobile app. Changes take effect immediately.",
                        },
                        {
                           question: "Are there any monthly fees?",
                           answer:
                              "No, there are no monthly fees for virtual cards. You only pay the one-time issuance fee when your card is approved.",
                        },
                        {
                           question:
                              "Can I use my virtual card internationally?",
                           answer:
                              "Yes, virtual cards can be used for international online purchases. Currency conversion fees may apply based on your card tier.",
                        },
                     ].map((faq, index) => (
                        <div
                           key={index}
                           className="border border-border rounded-lg p-4"
                        >
                           <h4 className="font-medium text-foreground mb-2">
                              {faq.question}
                           </h4>
                           <p className="text-sm text-muted-foreground">
                              {faq.answer}
                           </p>
                        </div>
                     ))}
                  </div>
               </motion.div>
            </motion.div>
         </div>
      </div>
   );
}
