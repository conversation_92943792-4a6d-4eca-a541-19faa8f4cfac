"use client";

import { <PERSON>Logo } from "@/components/bank-logo";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { ArrowLeft, CreditCard, Star, Shield, Percent } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function ApplyForCard() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [selectedCard, setSelectedCard] = useState("");
   const [formData, setFormData] = useState({
      annualIncome: "",
      employment: "",
      housingStatus: ""
   });

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const cardOptions = [
      {
         id: "classic",
         name: "Paramount Classic Card",
         type: "Credit Card",
         annualFee: "$0",
         cashback: "1%",
         features: ["No annual fee", "1% cashback on all purchases", "Fraud protection", "24/7 customer support"],
         icon: CreditCard,
         color: "bg-gradient-to-br from-slate-600 to-slate-800"
      },
      {
         id: "rewards",
         name: "Paramount Rewards Card",
         type: "Credit Card",
         annualFee: "$95",
         cashback: "2-5%",
         features: ["5% on rotating categories", "2% on gas and groceries", "1% on everything else", "Sign-up bonus"],
         icon: Star,
         color: "bg-gradient-to-br from-blue-600 to-blue-800"
      },
      {
         id: "premium",
         name: "Paramount Premium Card",
         type: "Credit Card",
         annualFee: "$450",
         cashback: "3%",
         features: ["3% on all purchases", "Travel insurance", "Airport lounge access", "Concierge service"],
         icon: Shield,
         color: "bg-gradient-to-br from-amber-600 to-amber-800"
      }
   ];

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({
         ...prev,
         [name]: value
      }));
   };

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (!selectedCard) return;
      
      const card = cardOptions.find(c => c.id === selectedCard);
      alert(`Application for ${card?.name} submitted successfully! You'll receive a decision within 7-10 business days.`);
      
      setSelectedCard("");
      setFormData({
         annualIncome: "",
         employment: "",
         housingStatus: ""
      });
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center gap-4">
                  <Button
                     variant="ghost"
                     size="sm"
                     onClick={() => router.push("/dashboard")}
                     className="flex items-center gap-2"
                  >
                     <ArrowLeft size={16} />
                     Back to Dashboard
                  </Button>
                  <BankLogo size="sm" />
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-4xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                     <CreditCard className="w-8 h-8 text-primary" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Apply for a Credit Card
                  </h1>
                  <p className="text-muted-foreground">
                     Choose the perfect card for your lifestyle and start earning rewards today.
                  </p>
               </div>

               {/* Card Selection */}
               <div className="mb-8">
                  <h2 className="text-xl font-semibold text-foreground mb-6">Choose Your Card</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                     {cardOptions.map((card) => {
                        const IconComponent = card.icon;
                        return (
                           <label
                              key={card.id}
                              className={`cursor-pointer transition-all ${
                                 selectedCard === card.id
                                    ? "ring-2 ring-primary"
                                    : "hover:shadow-lg"
                              }`}
                           >
                              <input
                                 type="radio"
                                 name="cardType"
                                 value={card.id}
                                 checked={selectedCard === card.id}
                                 onChange={(e) => setSelectedCard(e.target.value)}
                                 className="sr-only"
                              />
                              <div className="bank-card p-6 h-full">
                                 {/* Card Visual */}
                                 <div className={`${card.color} rounded-lg p-4 mb-4 text-white relative overflow-hidden`}>
                                    <div className="flex justify-between items-start mb-8">
                                       <IconComponent className="w-8 h-8" />
                                       <div className="text-right">
                                          <p className="text-xs opacity-80">Annual Fee</p>
                                          <p className="font-bold">{card.annualFee}</p>
                                       </div>
                                    </div>
                                    <div>
                                       <p className="text-xs opacity-80 mb-1">Cashback Rate</p>
                                       <p className="text-2xl font-bold">{card.cashback}</p>
                                    </div>
                                    <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
                                 </div>

                                 {/* Card Details */}
                                 <div>
                                    <h3 className="font-semibold text-foreground mb-1">{card.name}</h3>
                                    <p className="text-sm text-muted-foreground mb-4">{card.type}</p>
                                    
                                    <ul className="space-y-2">
                                       {card.features.map((feature, index) => (
                                          <li key={index} className="text-sm text-muted-foreground flex items-center gap-2">
                                             <div className="w-1.5 h-1.5 bg-success rounded-full"></div>
                                             {feature}
                                          </li>
                                       ))}
                                    </ul>
                                 </div>
                              </div>
                           </label>
                        );
                     })}
                  </div>
               </div>

               {/* Application Form */}
               {selectedCard && (
                  <motion.div
                     initial={{ opacity: 0, y: 20 }}
                     animate={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.4 }}
                     className="bank-card p-6"
                  >
                     <h2 className="text-xl font-semibold text-foreground mb-6">Application Information</h2>
                     <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                           <Input
                              label="Annual Income"
                              name="annualIncome"
                              type="number"
                              value={formData.annualIncome}
                              onChange={handleInputChange}
                              placeholder="Enter your annual income"
                              required
                           />

                           <div>
                              <label className="text-sm font-medium text-foreground mb-2 block">
                                 Employment Status
                              </label>
                              <select
                                 name="employment"
                                 value={formData.employment}
                                 onChange={handleInputChange}
                                 className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                 required
                              >
                                 <option value="">Select employment status</option>
                                 <option value="employed">Employed</option>
                                 <option value="self-employed">Self-Employed</option>
                                 <option value="student">Student</option>
                                 <option value="retired">Retired</option>
                                 <option value="unemployed">Unemployed</option>
                              </select>
                           </div>

                           <div>
                              <label className="text-sm font-medium text-foreground mb-2 block">
                                 Housing Status
                              </label>
                              <select
                                 name="housingStatus"
                                 value={formData.housingStatus}
                                 onChange={handleInputChange}
                                 className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                 required
                              >
                                 <option value="">Select housing status</option>
                                 <option value="own">Own</option>
                                 <option value="rent">Rent</option>
                                 <option value="mortgage">Mortgage</option>
                                 <option value="other">Other</option>
                              </select>
                           </div>
                        </div>

                        <div className="bg-muted/30 rounded-lg p-4">
                           <p className="text-sm text-muted-foreground">
                              By submitting this application, you authorize Paramount Bank to check your credit report and verify the information provided. Your application will be reviewed and you'll receive a decision within 7-10 business days.
                           </p>
                        </div>

                        <Button
                           type="submit"
                           variant="premium"
                           size="lg"
                           className="w-full"
                           disabled={!formData.annualIncome || !formData.employment || !formData.housingStatus}
                        >
                           Submit Application
                        </Button>
                     </form>
                  </motion.div>
               )}
            </motion.div>
         </main>
      </div>
   );
}
