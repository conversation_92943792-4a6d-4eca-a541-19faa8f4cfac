"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import {
   ArrowDown,
   ArrowUp,
   Coffee,
   Download,
   Filter,
   History,
   Home,
   Search,
   ShoppingCart,
   Smartphone,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function AccountHistory() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [filterType, setFilterType] = useState("all");
   const [searchTerm, setSearchTerm] = useState("");

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   // Extended transaction data
   const allTransactions = [
      {
         id: "TXN-001",
         type: "debit",
         amount: 45.99,
         description: "Starbucks Coffee",
         merchant: "Starbucks",
         date: new Date(Date.now() - 2 * 60 * 60 * 1000),
         icon: Coffee,
         category: "Food & Dining",
         status: "completed",
      },
      {
         id: "TXN-002",
         type: "credit",
         amount: 2500.0,
         description: "Salary Deposit",
         merchant: "Acme Corp",
         date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
         icon: ArrowDown,
         category: "Income",
         status: "completed",
      },
      {
         id: "TXN-003",
         type: "debit",
         amount: 89.5,
         description: "Grocery Shopping",
         merchant: "Walmart",
         date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
         icon: ShoppingCart,
         category: "Groceries",
         status: "completed",
      },
      {
         id: "TXN-004",
         type: "debit",
         amount: 1200.0,
         description: "Rent Payment",
         merchant: "Property Management",
         date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
         icon: Home,
         category: "Housing",
         status: "completed",
      },
      {
         id: "TXN-005",
         type: "debit",
         amount: 299.99,
         description: "iPhone Case",
         merchant: "Apple Store",
         date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
         icon: Smartphone,
         category: "Electronics",
         status: "completed",
      },
      {
         id: "TXN-006",
         type: "debit",
         amount: 75.0,
         description: "Gas Station",
         merchant: "Shell",
         date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
         icon: ArrowUp,
         category: "Transportation",
         status: "completed",
      },
      {
         id: "TXN-007",
         type: "credit",
         amount: 150.0,
         description: "Cashback Reward",
         merchant: "Paramount Bank",
         date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
         icon: ArrowDown,
         category: "Rewards",
         status: "completed",
      },
   ];

   const filteredTransactions = allTransactions.filter((transaction) => {
      const matchesType =
         filterType === "all" || transaction.type === filterType;
      const matchesSearch =
         transaction.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.merchant
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
         transaction.category.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesType && matchesSearch;
   });

   const formatDate = (date: Date) => {
      return date.toLocaleDateString("en-US", {
         year: "numeric",
         month: "short",
         day: "numeric",
         hour: "2-digit",
         minute: "2-digit",
      });
   };

   const handleExport = () => {
      alert("Transaction history exported successfully!");
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb items={[{ label: "Account History" }]} />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
            >
               <div className="flex items-center justify-between mb-8">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Transaction History
                     </h1>
                     <p className="text-muted-foreground">
                        View and manage your account transactions
                     </p>
                  </div>
                  <Button
                     variant="outline"
                     onClick={handleExport}
                     className="flex items-center gap-2"
                  >
                     <Download size={16} />
                     Export
                  </Button>
               </div>

               {/* Filters and Search */}
               <div className="bank-card p-6 mb-6">
                  <div className="flex flex-col md:flex-row gap-4">
                     <div className="flex-1">
                        <div className="relative">
                           <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                           <input
                              type="text"
                              placeholder="Search transactions..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground"
                           />
                        </div>
                     </div>
                     <div className="flex gap-2">
                        <select
                           value={filterType}
                           onChange={(e) => setFilterType(e.target.value)}
                           className="px-4 py-2 border border-border rounded-lg bg-background text-foreground"
                        >
                           <option value="all">All Transactions</option>
                           <option value="credit">Credits Only</option>
                           <option value="debit">Debits Only</option>
                        </select>
                     </div>
                  </div>
               </div>

               {/* Transactions List */}
               <div className="bank-card p-6">
                  <div className="space-y-4">
                     {filteredTransactions.length === 0 ? (
                        <div className="text-center py-8">
                           <History className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                           <p className="text-muted-foreground">
                              No transactions found
                           </p>
                        </div>
                     ) : (
                        filteredTransactions.map((transaction) => {
                           const IconComponent = transaction.icon;
                           return (
                              <div
                                 key={transaction.id}
                                 className="flex items-center justify-between p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors"
                              >
                                 <div className="flex items-center gap-4">
                                    <div
                                       className={`p-2 rounded-lg ${
                                          transaction.type === "credit"
                                             ? "bg-success/10"
                                             : "bg-muted"
                                       }`}
                                    >
                                       <IconComponent
                                          className={`w-5 h-5 ${
                                             transaction.type === "credit"
                                                ? "text-success"
                                                : "text-muted-foreground"
                                          }`}
                                       />
                                    </div>
                                    <div>
                                       <p className="font-medium text-foreground">
                                          {transaction.description}
                                       </p>
                                       <p className="text-sm text-muted-foreground">
                                          {transaction.merchant} •{" "}
                                          {transaction.category}
                                       </p>
                                       <p className="text-xs text-muted-foreground">
                                          {formatDate(transaction.date)}
                                       </p>
                                    </div>
                                 </div>
                                 <div className="text-right">
                                    <p
                                       className={`font-semibold ${
                                          transaction.type === "credit"
                                             ? "text-success"
                                             : "text-foreground"
                                       }`}
                                    >
                                       {transaction.type === "credit"
                                          ? "+"
                                          : "-"}
                                       $
                                       {transaction.amount.toLocaleString(
                                          "en-US",
                                          { minimumFractionDigits: 2 }
                                       )}
                                    </p>
                                    <p className="text-xs text-muted-foreground capitalize">
                                       {transaction.status}
                                    </p>
                                 </div>
                              </div>
                           );
                        })
                     )}
                  </div>
               </div>
            </motion.div>
         </div>
      </div>
   );
}
