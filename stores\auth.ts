import { create } from "zustand";
import { persist } from "zustand/middleware";

interface User {
   id: string;
   email: string;
   firstName: string;
   lastName: string;
   username: string;
   phoneNumber: string;
   country: string;
   accountType: string;
}

interface AuthState {
   isAuthenticated: boolean;
   user: User | null;
   login: (email: string, password: string) => Promise<boolean>;
   logout: () => void;
   register: (
      userData: Partial<User> & { email: string; password: string }
   ) => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
   persist(
      (set) => ({
         isAuthenticated: false,
         user: null,

         login: async (email: string, password: string) => {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Simple demo login - in real app, this would call your API
            if (email && password) {
               const user: User = {
                  id: "1",
                  email,
                  firstName: "<PERSON>",
                  lastName: "<PERSON><PERSON>",
                  username: "johndo<PERSON>",
                  phoneNumber: "+**********",
                  country: "United States",
                  accountType: "Checking Account",
               };

               set({ isAuthenticated: true, user });
               return true;
            }
            return false;
         },

         logout: () => {
            set({ isAuthenticated: false, user: null });
         },

         register: async (userData) => {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1500));

            const user: User = {
               id: Date.now().toString(),
               firstName: userData.firstName || "",
               lastName: userData.lastName || "",
               username: userData.username || "",
               email: userData.email,
               phoneNumber: userData.phoneNumber || "",
               country: userData.country || "",
               accountType: userData.accountType || "Checking Account",
            };

            set({ isAuthenticated: true, user });
            return true;
         },
      }),
      {
         name: "paramount-auth",
         partialize: (state) => ({
            isAuthenticated: state.isAuthenticated,
            user: state.user,
         }),
      }
   )
);
