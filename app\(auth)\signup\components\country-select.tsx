"use client";

import { Globe } from "lucide-react";
import { useEffect, useState } from "react";
import { CustomSelect, SelectItem } from "./custom-select";

interface Country {
  name: {
    common: string;
  };
  cca2: string;
  flags: {
    svg: string;
    png: string;
  };
}

interface CountrySelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  error?: string;
}

export function CountrySelect({ value, onValueChange, error }: CountrySelectProps) {
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCountries = async () => {
      setIsLoading(true);
      setLoadError(null);
      
      try {
        const response = await fetch("https://restcountries.com/v3.1/all?fields=name,cca2,flags");
        
        if (!response.ok) {
          throw new Error(`Failed to fetch countries: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Sort countries alphabetically by name
        const sortedCountries = data.sort((a: Country, b: Country) => 
          a.name.common.localeCompare(b.name.common)
        );
        
        setCountries(sortedCountries);
      } catch (error) {
        console.error("Error fetching countries:", error);
        setLoadError("Failed to load countries. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCountries();
  }, []);

  return (
    <CustomSelect
      label="Country"
      placeholder={isLoading ? "Loading countries..." : "Select your country"}
      icon={<Globe size={18} />}
      value={value}
      onValueChange={onValueChange}
      error={error || loadError}
      disabled={isLoading}
    >
      {isLoading ? (
        <SelectItem value="loading" disabled>
          Loading countries...
        </SelectItem>
      ) : loadError ? (
        <SelectItem value="error" disabled>
          Error loading countries
        </SelectItem>
      ) : (
        countries.map((country) => (
          <SelectItem key={country.cca2} value={country.name.common}>
            <div className="flex items-center gap-2">
              <img 
                src={country.flags.svg} 
                alt={`${country.name.common} flag`} 
                className="w-4 h-3 object-cover"
              />
              {country.name.common}
            </div>
          </SelectItem>
        ))
      )}
    </CustomSelect>
  );
}
