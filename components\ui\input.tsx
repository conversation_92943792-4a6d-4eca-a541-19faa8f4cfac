import { cn } from "@/lib/utils";
import * as React from "react";

export interface InputProps
   extends React.InputHTMLAttributes<HTMLInputElement> {
   label?: string;
   error?: string;
   icon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
   ({ className, type, label, error, icon, ...props }, ref) => {
      return (
         <div className="space-y-2">
            {label && (
               <label className="text-sm font-medium text-foreground">
                  {label}
               </label>
            )}
            <div className="relative">
               {icon && (
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground z-5">
                     {icon}
                  </div>
               )}
               <input
                  type={type}
                  className={cn(
                     "bank-input flex h-12 w-full rounded-lg px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
                     icon && "pl-10",
                     error &&
                        "border-destructive focus:border-destructive focus:ring-destructive/20",
                     className
                  )}
                  ref={ref}
                  {...props}
               />
            </div>
            {error && <p className="text-sm text-destructive">{error}</p>}
         </div>
      );
   }
);
Input.displayName = "Input";

export { Input };
