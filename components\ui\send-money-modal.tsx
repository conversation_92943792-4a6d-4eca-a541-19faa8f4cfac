"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ArrowLeftRight, Globe, X, Info } from "lucide-react";
import * as React from "react";
import { Button } from "./button";
import { Modal } from "./modal";

export interface SendMoneyModalProps {
   isOpen: boolean;
   onClose: () => void;
   onLocalTransfer: () => void;
   onInternationalTransfer: () => void;
}

const SendMoneyModal = React.forwardRef<HTMLDivElement, SendMoneyModalProps>(
   ({ isOpen, onClose, onLocalTransfer, onInternationalTransfer }, ref) => {
      return (
         <Modal
            isOpen={isOpen}
            onClose={onClose}
            title="Send Money"
            size="md"
            ref={ref}
         >
            <div className="space-y-6">
               <p className="text-muted-foreground">
                  Choose your transfer type to send money securely.
               </p>

               <div className="grid gap-4">
                  {/* Local Transfer Option */}
                  <motion.div
                     whileHover={{ scale: 1.02 }}
                     whileTap={{ scale: 0.98 }}
                     className="border border-border rounded-lg p-6 cursor-pointer hover:border-primary/50 hover:bg-primary/5 transition-all"
                     onClick={() => {
                        onLocalTransfer();
                        onClose();
                     }}
                  >
                     <div className="flex items-start gap-4">
                        <div className="p-3 bg-primary/10 rounded-lg">
                           <ArrowLeftRight className="w-6 h-6 text-primary" />
                        </div>
                        <div className="flex-1">
                           <h3 className="font-semibold text-lg text-foreground mb-2">
                              Local Transfer
                           </h3>
                           <p className="text-muted-foreground text-sm mb-3">
                              Send money to other accounts within the same country. 
                              Fast, secure, and usually processed instantly.
                           </p>
                           <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Info className="w-3 h-3" />
                              <span>Processing time: Instant • Fee: Free</span>
                           </div>
                        </div>
                     </div>
                  </motion.div>

                  {/* International Transfer Option */}
                  <motion.div
                     whileHover={{ scale: 1.02 }}
                     whileTap={{ scale: 0.98 }}
                     className="border border-border rounded-lg p-6 cursor-pointer hover:border-success/50 hover:bg-success/5 transition-all"
                     onClick={() => {
                        onInternationalTransfer();
                        onClose();
                     }}
                  >
                     <div className="flex items-start gap-4">
                        <div className="p-3 bg-success/10 rounded-lg">
                           <Globe className="w-6 h-6 text-success" />
                        </div>
                        <div className="flex-1">
                           <h3 className="font-semibold text-lg text-foreground mb-2">
                              International Wire Transfer
                           </h3>
                           <p className="text-muted-foreground text-sm mb-3">
                              Send money internationally using SWIFT network. 
                              Secure global transfers with competitive exchange rates.
                           </p>
                           <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Info className="w-3 h-3" />
                              <span>Processing time: 1-3 business days • Fee: $15-25</span>
                           </div>
                        </div>
                     </div>
                  </motion.div>
               </div>

               <div className="bg-muted/30 rounded-lg p-4">
                  <h4 className="font-medium text-foreground mb-2">Transfer Definitions</h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                     <div>
                        <strong>Local Transfer:</strong> Transfers between accounts in the same country using domestic payment networks.
                     </div>
                     <div>
                        <strong>International Wire:</strong> Cross-border transfers using the SWIFT network for global reach.
                     </div>
                  </div>
               </div>

               <div className="flex justify-end">
                  <Button variant="outline" onClick={onClose}>
                     Cancel
                  </Button>
               </div>
            </div>
         </Modal>
      );
   }
);

SendMoneyModal.displayName = "SendMoneyModal";

export { SendMoneyModal };
