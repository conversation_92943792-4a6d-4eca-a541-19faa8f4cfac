"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/bank-logo";
import { But<PERSON> } from "@/components/ui/button";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { <PERSON><PERSON>eft, Bell, Check, Trash2, Filter } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Notification {
   id: string;
   title: string;
   message: string;
   type: "info" | "success" | "warning" | "error";
   timestamp: Date;
   read: boolean;
}

export default function Notifications() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [filter, setFilter] = useState("all");
   const [notifications, setNotifications] = useState<Notification[]>([
      {
         id: "notif-1",
         title: "Payment Received",
         message: "You received $2,500.00 from Acme Corp for salary payment",
         type: "success",
         timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
         read: false
      },
      {
         id: "notif-2",
         title: "Card Transaction",
         message: "Purchase at Starbucks for $45.99 using your Paramount Classic Card",
         type: "info",
         timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
         read: false
      },
      {
         id: "notif-3",
         title: "Security Alert",
         message: "New device login detected from Chrome on Windows. If this wasn't you, please contact support immediately.",
         type: "warning",
         timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
         read: true
      },
      {
         id: "notif-4",
         title: "Monthly Statement",
         message: "Your December statement is now available for download in your account.",
         type: "info",
         timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
         read: true
      },
      {
         id: "notif-5",
         title: "Low Balance Alert",
         message: "Your account balance has fallen below $500. Consider adding funds to avoid overdraft fees.",
         type: "warning",
         timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
         read: true
      },
      {
         id: "notif-6",
         title: "Transfer Completed",
         message: "Your transfer of $1,200.00 to Property Management has been completed successfully.",
         type: "success",
         timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
         read: true
      }
   ]);

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const filteredNotifications = notifications.filter(notification => {
      if (filter === "unread") return !notification.read;
      if (filter === "read") return notification.read;
      return true;
   });

   const getTypeColor = (type: Notification["type"]) => {
      switch (type) {
         case "success":
            return "text-success bg-success/10 border-success/20";
         case "warning":
            return "text-warning bg-warning/10 border-warning/20";
         case "error":
            return "text-destructive bg-destructive/10 border-destructive/20";
         default:
            return "text-primary bg-primary/10 border-primary/20";
      }
   };

   const formatTime = (date: Date) => {
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);

      if (minutes < 1) return "Just now";
      if (minutes < 60) return `${minutes}m ago`;
      if (hours < 24) return `${hours}h ago`;
      return `${days}d ago`;
   };

   const markAsRead = (id: string) => {
      setNotifications(prev =>
         prev.map(notif =>
            notif.id === id ? { ...notif, read: true } : notif
         )
      );
   };

   const markAllAsRead = () => {
      setNotifications(prev =>
         prev.map(notif => ({ ...notif, read: true }))
      );
   };

   const deleteNotification = (id: string) => {
      setNotifications(prev => prev.filter(notif => notif.id !== id));
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center gap-4">
                  <Button
                     variant="ghost"
                     size="sm"
                     onClick={() => router.push("/dashboard")}
                     className="flex items-center gap-2"
                  >
                     <ArrowLeft size={16} />
                     Back to Dashboard
                  </Button>
                  <BankLogo size="sm" />
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-4xl mx-auto"
            >
               <div className="flex items-center justify-between mb-8">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Notifications
                     </h1>
                     <p className="text-muted-foreground">
                        Stay updated with your account activity and important alerts
                     </p>
                  </div>
                  <Button
                     variant="outline"
                     onClick={markAllAsRead}
                     className="flex items-center gap-2"
                  >
                     <Check size={16} />
                     Mark All Read
                  </Button>
               </div>

               {/* Filters */}
               <div className="bank-card p-4 mb-6">
                  <div className="flex items-center gap-4">
                     <Filter className="w-4 h-4 text-muted-foreground" />
                     <div className="flex gap-2">
                        <Button
                           variant={filter === "all" ? "default" : "ghost"}
                           size="sm"
                           onClick={() => setFilter("all")}
                        >
                           All ({notifications.length})
                        </Button>
                        <Button
                           variant={filter === "unread" ? "default" : "ghost"}
                           size="sm"
                           onClick={() => setFilter("unread")}
                        >
                           Unread ({notifications.filter(n => !n.read).length})
                        </Button>
                        <Button
                           variant={filter === "read" ? "default" : "ghost"}
                           size="sm"
                           onClick={() => setFilter("read")}
                        >
                           Read ({notifications.filter(n => n.read).length})
                        </Button>
                     </div>
                  </div>
               </div>

               {/* Notifications List */}
               <div className="space-y-4">
                  {filteredNotifications.length === 0 ? (
                     <div className="bank-card p-8 text-center">
                        <Bell className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No notifications found</p>
                     </div>
                  ) : (
                     filteredNotifications.map((notification) => (
                        <motion.div
                           key={notification.id}
                           initial={{ opacity: 0, x: -20 }}
                           animate={{ opacity: 1, x: 0 }}
                           className={`bank-card p-6 transition-all ${
                              notification.read ? "opacity-75" : ""
                           }`}
                        >
                           <div className="flex items-start gap-4">
                              <div className={`w-3 h-3 rounded-full mt-2 flex-shrink-0 ${
                                 notification.read ? "bg-muted-foreground" : getTypeColor(notification.type).split(' ')[0]
                              }`} />
                              
                              <div className="flex-1 min-w-0">
                                 <div className="flex items-start justify-between mb-2">
                                    <h3 className="font-semibold text-foreground">
                                       {notification.title}
                                    </h3>
                                    <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                                       <span className="text-xs text-muted-foreground">
                                          {formatTime(notification.timestamp)}
                                       </span>
                                       {!notification.read && (
                                          <Button
                                             variant="ghost"
                                             size="sm"
                                             onClick={() => markAsRead(notification.id)}
                                             className="h-6 w-6 p-0"
                                          >
                                             <Check className="w-3 h-3" />
                                          </Button>
                                       )}
                                       <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => deleteNotification(notification.id)}
                                          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                       >
                                          <Trash2 className="w-3 h-3" />
                                       </Button>
                                    </div>
                                 </div>
                                 <p className="text-muted-foreground text-sm leading-relaxed">
                                    {notification.message}
                                 </p>
                                 <div className={`inline-block px-2 py-1 rounded text-xs mt-2 ${getTypeColor(notification.type)}`}>
                                    {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                                 </div>
                              </div>
                           </div>
                        </motion.div>
                     ))
                  )}
               </div>
            </motion.div>
         </main>
      </div>
   );
}
