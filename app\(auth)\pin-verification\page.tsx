"use client";

import { <PERSON>Logo } from "@/components/bank-logo";
import { But<PERSON> } from "@/components/ui/button";
import { PinInput } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { ArrowLeft, RefreshCw, Shield } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function PinVerification() {
   const [pin, setPin] = useState("");
   const [error, setError] = useState("");
   const [isLoading, setIsLoading] = useState(false);
   const [attempts, setAttempts] = useState(0);
   const [isLocked, setIsLocked] = useState(false);
   const [lockTimeRemaining, setLockTimeRemaining] = useState(0);

   const router = useRouter();
   const { isAuthenticated, isPinVerified, user, verifyPin } = useAuthStore();

   // Redirect if not authenticated or already PIN verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.replace("/login");
      } else if (isPinVerified) {
         router.replace("/dashboard");
      }
   }, [isAuthenticated, isPinVerified, router]);

   // Handle lockout timer
   useEffect(() => {
      let interval: NodeJS.Timeout;
      if (isLocked && lockTimeRemaining > 0) {
         interval = setInterval(() => {
            setLockTimeRemaining((prev) => {
               if (prev <= 1) {
                  setIsLocked(false);
                  setAttempts(0);
                  return 0;
               }
               return prev - 1;
            });
         }, 1000);
      }
      return () => clearInterval(interval);
   }, [isLocked, lockTimeRemaining]);

   const handlePinSubmit = async () => {
      if (pin.length !== 4) {
         setError("Please enter a 4-digit PIN");
         return;
      }

      if (isLocked) {
         setError(
            `Too many attempts. Try again in ${lockTimeRemaining} seconds.`
         );
         return;
      }

      setIsLoading(true);
      setError("");

      try {
         const success = await verifyPin(pin);

         if (success) {
            // PIN verified successfully
            router.push("/dashboard");
         } else {
            const newAttempts = attempts + 1;
            setAttempts(newAttempts);

            if (newAttempts >= 3) {
               setIsLocked(true);
               setLockTimeRemaining(300); // 5 minutes lockout
               setError(
                  "Too many failed attempts. Account locked for 5 minutes."
               );
            } else {
               setError(
                  `Incorrect PIN. ${3 - newAttempts} attempts remaining.`
               );
            }
            setPin("");
         }
      } catch (error) {
         console.error("PIN verification error:", error);
         setError("Verification failed. Please try again.");
      } finally {
         setIsLoading(false);
      }
   };

   const handleResendPin = async () => {
      setIsLoading(true);
      try {
         // Simulate resend PIN API call
         await new Promise((resolve) => setTimeout(resolve, 1000));
         // Show success message (in real app, this would trigger SMS/email)
         alert("A new PIN has been sent to your registered phone number.");
      } catch (error) {
         console.error("Resend PIN error:", error);
         setError("Failed to resend PIN. Please try again.");
      } finally {
         setIsLoading(false);
      }
   };

   const formatTime = (seconds: number) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
   };

   if (!isAuthenticated) {
      return null; // Will redirect
   }

   return (
      <div className="min-h-screen bg-gradient-to-br from-background via-secondary/30 to-background flex items-center justify-center p-4">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md"
         >
            <div className="text-center mb-8 lg:hidden">
               <BankLogo size="md" />
            </div>

            <div className="bank-card p-8">
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                     <Shield className="w-8 h-8 text-primary" />
                  </div>
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                     PIN Verification
                  </h1>
                  <p className="text-muted-foreground">
                     Enter your 4-digit PIN to access your account
                  </p>
                  {user && (
                     <p className="text-sm text-muted-foreground mt-2">
                        Welcome back, {user.firstName}
                     </p>
                  )}
               </div>

               <div className="space-y-6">
                  <PinInput
                     length={4}
                     value={pin}
                     onChange={setPin}
                     error={error}
                     disabled={isLoading || isLocked}
                  />

                  {isLocked && (
                     <div className="text-center p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                        <p className="text-sm text-destructive">
                           Account temporarily locked
                        </p>
                        <p className="text-xs text-destructive/80 mt-1">
                           Time remaining: {formatTime(lockTimeRemaining)}
                        </p>
                     </div>
                  )}

                  <Button
                     onClick={handlePinSubmit}
                     variant="premium"
                     size="lg"
                     className="w-full"
                     disabled={isLoading || pin.length !== 4 || isLocked}
                  >
                     {isLoading ? (
                        <>
                           <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                           Verifying...
                        </>
                     ) : (
                        "Verify PIN"
                     )}
                  </Button>

                  <div className="flex items-center justify-between text-sm">
                     <Button
                        variant="ghost"
                        onClick={() => router.push("/login")}
                        className="text-muted-foreground hover:text-foreground"
                     >
                        <ArrowLeft className="w-4 h-4 mr-1" />
                        Back to Login
                     </Button>

                     <Button
                        variant="ghost"
                        onClick={handleResendPin}
                        disabled={isLoading || isLocked}
                        className="text-primary hover:text-primary-light"
                     >
                        Resend PIN
                     </Button>
                  </div>
               </div>
            </div>

            <div className="mt-6 text-center">
               <p className="text-xs text-muted-foreground">
                  For demo purposes, use PIN: 1234
               </p>
               <p className="text-xs text-muted-foreground mt-1">
                  Protected by 256-bit SSL encryption
               </p>
            </div>
         </motion.div>
      </div>
   );
}
