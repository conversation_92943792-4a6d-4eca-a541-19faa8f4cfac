import { Building2 } from "lucide-react";

interface BankLogoProps {
   size?: "sm" | "md" | "lg";
}

export const BankLogo = ({ size = "md" }: BankLogoProps) => {
   const sizeClasses = {
      sm: "text-2xl",
      md: "text-3xl",
      lg: "text-4xl",
   };

   const iconSizes = {
      sm: 24,
      md: 32,
      lg: 40,
   };

   return (
      <div className="flex items-center gap-3">
         <div className="p-2 bg-gradient-primary rounded-xl">
            <Building2
               size={iconSizes[size]}
               className="text-primary-foreground"
            />
         </div>
         <div className="flex flex-col">
            <h1 className={`font-bold text-primary ${sizeClasses[size]}`}>
               Paramount
            </h1>
            <p className="text-xs text-muted-foreground -mt-1">BANK</p>
         </div>
      </div>
   );
};
