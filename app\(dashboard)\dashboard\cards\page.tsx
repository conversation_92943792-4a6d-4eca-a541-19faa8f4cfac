"use client";

import { <PERSON>Logo } from "@/components/bank-logo";
import { But<PERSON> } from "@/components/ui/button";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import {
   ArrowLeft,
   CheckCircle,
   ChevronDown,
   ChevronUp,
   Clock,
   CreditCard,
   DollarSign,
   Plus,
   Shield,
   Smartphone,
   Zap,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function Cards() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.replace("/login");
      } else if (!isPinVerified) {
         router.replace("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const cardStats = [
      {
         title: "Active Cards",
         value: "0",
         icon: CreditCard,
         color: "text-success bg-success/10",
      },
      {
         title: "Pending Applications",
         value: "0",
         icon: Clock,
         color: "text-warning bg-warning/10",
      },
      {
         title: "Total Card Balance",
         value: "$0.00",
         icon: DollarSign,
         color: "text-primary bg-primary/10",
      },
   ];

   const virtualCardFeatures = [
      "Instant issuance - get your card in seconds",
      "Enhanced security with dynamic CVV",
      "Spending controls and limits",
      "Real-time transaction notifications",
      "Freeze/unfreeze anytime",
      "Perfect for online shopping",
   ];

   const howItWorks = [
      {
         step: "1",
         title: "Apply",
         description:
            "Choose your card type and complete the application in minutes",
         icon: Plus,
      },
      {
         step: "2",
         title: "Activate",
         description: "Your virtual card is instantly available after approval",
         icon: Zap,
      },
      {
         step: "3",
         title: "Use",
         description:
            "Start making secure payments online and in-app immediately",
         icon: Smartphone,
      },
   ];

   const faqs = [
      {
         question: "What is a virtual card?",
         answer:
            "A virtual card is a digital version of a physical credit card that exists only online. It has all the same features as a physical card but provides enhanced security for online transactions.",
      },
      {
         question: "How quickly can I get my virtual card?",
         answer:
            "Virtual cards are issued instantly upon approval. You can start using your card immediately after completing the application process.",
      },
      {
         question: "Are virtual cards secure?",
         answer:
            "Yes, virtual cards are extremely secure. They feature dynamic CVV codes, spending limits, and can be frozen instantly if needed. They're perfect for online shopping and subscription services.",
      },
      {
         question: "Can I use virtual cards for in-store purchases?",
         answer:
            "Virtual cards can be used for contactless payments through mobile wallets like Apple Pay, Google Pay, and Samsung Pay at participating merchants.",
      },
      {
         question: "What are the fees for virtual cards?",
         answer:
            "There's a one-time issuance fee based on the card tier: Standard ($5), Gold ($15), Platinum ($25), and Black ($50). No monthly fees apply.",
      },
      {
         question: "Can I set spending limits?",
         answer:
            "Yes, you can set daily, weekly, and monthly spending limits for your virtual cards. You can also restrict usage to specific merchant categories.",
      },
   ];

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center gap-4">
                  <Button
                     variant="ghost"
                     size="sm"
                     onClick={() => router.push("/dashboard")}
                     className="flex items-center gap-2"
                  >
                     <ArrowLeft size={16} />
                     Back to Dashboard
                  </Button>
                  <BankLogo size="sm" />
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
            >
               <div className="mb-8">
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Your Cards
                  </h1>
                  <p className="text-muted-foreground">
                     Manage your cards and apply for new virtual cards
                  </p>
               </div>

               {/* Card Statistics */}
               <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {cardStats.map((stat, index) => {
                     const IconComponent = stat.icon;
                     return (
                        <motion.div
                           key={stat.title}
                           initial={{ opacity: 0, y: 20 }}
                           animate={{ opacity: 1, y: 0 }}
                           transition={{ duration: 0.6, delay: index * 0.1 }}
                           className="bank-card p-6"
                        >
                           <div className="flex items-center justify-between">
                              <div>
                                 <p className="text-sm text-muted-foreground mb-1">
                                    {stat.title}
                                 </p>
                                 <p className="text-2xl font-bold text-foreground">
                                    {stat.value}
                                 </p>
                              </div>
                              <div className={`p-3 rounded-lg ${stat.color}`}>
                                 <IconComponent className="w-6 h-6" />
                              </div>
                           </div>
                        </motion.div>
                     );
                  })}
               </div>

               {/* Virtual Card Promotion */}
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="bank-card p-8 mb-8"
               >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                     <div>
                        <h2 className="text-2xl font-bold text-foreground mb-4">
                           Get Your Virtual Card Today
                        </h2>
                        <p className="text-muted-foreground mb-6">
                           Experience the future of digital payments with our
                           secure virtual cards. Perfect for online shopping,
                           subscriptions, and contactless payments.
                        </p>

                        <div className="space-y-3 mb-6">
                           {virtualCardFeatures.map((feature, index) => (
                              <div
                                 key={index}
                                 className="flex items-center gap-3"
                              >
                                 <CheckCircle className="w-5 h-5 text-success flex-shrink-0" />
                                 <span className="text-sm text-foreground">
                                    {feature}
                                 </span>
                              </div>
                           ))}
                        </div>

                        <Button
                           variant="premium"
                           size="lg"
                           onClick={() => router.push("/dashboard/cards/apply")}
                           className="w-full sm:w-auto"
                        >
                           <Plus className="w-4 h-4 mr-2" />
                           Apply for Virtual Card
                        </Button>
                     </div>

                     {/* Virtual Card Mockup */}
                     <div className="flex justify-center">
                        <div className="relative">
                           <div className="w-80 h-48 bg-gradient-to-br from-primary via-primary-light to-primary-dark rounded-xl p-6 text-primary-foreground shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
                              <div className="flex justify-between items-start mb-8">
                                 <div>
                                    <p className="text-xs opacity-80">
                                       Virtual Card
                                    </p>
                                    <p className="font-semibold">
                                       Paramount Bank
                                    </p>
                                 </div>
                                 <div className="w-8 h-8 bg-primary-foreground/20 rounded-full"></div>
                              </div>
                              <div className="space-y-4">
                                 <div>
                                    <p className="text-xs opacity-80">
                                       Card Number
                                    </p>
                                    <p className="font-mono text-lg">
                                       •••• •••• •••• 1234
                                    </p>
                                 </div>
                                 <div className="flex justify-between">
                                    <div>
                                       <p className="text-xs opacity-80">
                                          Valid Thru
                                       </p>
                                       <p className="font-mono">12/28</p>
                                    </div>
                                    <div>
                                       <p className="text-xs opacity-80">CVV</p>
                                       <p className="font-mono">•••</p>
                                    </div>
                                 </div>
                              </div>
                              <div className="absolute top-0 right-0 w-20 h-20 bg-primary-foreground/10 rounded-full -translate-y-10 translate-x-10"></div>
                           </div>
                        </div>
                     </div>
                  </div>
               </motion.div>

               {/* Your Cards Section (Empty State) */}
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="bank-card p-6 mb-8"
               >
                  <div className="flex items-center gap-3 mb-6">
                     <div className="p-2 bg-primary/10 rounded-lg">
                        <CreditCard className="w-5 h-5 text-primary" />
                     </div>
                     <h2 className="text-xl font-semibold text-foreground">
                        Your Cards
                     </h2>
                  </div>

                  {/* Empty State */}
                  <div className="text-center py-12">
                     <div className="mx-auto w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                        <CreditCard className="w-8 h-8 text-muted-foreground" />
                     </div>
                     <h3 className="text-lg font-semibold text-foreground mb-2">
                        No Cards Yet
                     </h3>
                     <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                        Apply for a Paramount Bank card to enjoy exclusive
                        benefits, rewards, and secure payments worldwide.
                     </p>
                     <Button
                        variant="premium"
                        onClick={() => router.push("/dashboard/cards/apply")}
                        className="inline-flex items-center gap-2"
                     >
                        <Plus className="w-4 h-4" />
                        Apply for Card
                     </Button>
                  </div>
               </motion.div>

               {/* How Virtual Cards Work */}
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  className="bank-card p-8 mb-8"
               >
                  <div className="text-center mb-8">
                     <h2 className="text-2xl font-bold text-foreground mb-2">
                        How Virtual Cards Work
                     </h2>
                     <p className="text-muted-foreground">
                        Get started with your virtual card in three simple steps
                     </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                     {howItWorks.map((step, index) => {
                        const IconComponent = step.icon;
                        return (
                           <div key={step.step} className="text-center">
                              <div className="relative mb-6">
                                 <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                                    <IconComponent className="w-8 h-8 text-primary" />
                                 </div>
                                 <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                                    {step.step}
                                 </div>
                              </div>
                              <h3 className="text-lg font-semibold text-foreground mb-2">
                                 {step.title}
                              </h3>
                              <p className="text-muted-foreground text-sm">
                                 {step.description}
                              </p>
                           </div>
                        );
                     })}
                  </div>
               </motion.div>

               {/* FAQ Section */}
               <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="bank-card p-8"
               >
                  <div className="text-center mb-8">
                     <h2 className="text-2xl font-bold text-foreground mb-2">
                        Frequently Asked Questions
                     </h2>
                     <p className="text-muted-foreground">
                        Everything you need to know about virtual cards
                     </p>
                  </div>

                  <div className="space-y-4">
                     {faqs.map((faq, index) => (
                        <div
                           key={index}
                           className="border border-border rounded-lg"
                        >
                           <button
                              onClick={() =>
                                 setExpandedFaq(
                                    expandedFaq === index ? null : index
                                 )
                              }
                              className="w-full p-4 text-left flex items-center justify-between hover:bg-muted/30 transition-colors"
                           >
                              <span className="font-medium text-foreground">
                                 {faq.question}
                              </span>
                              {expandedFaq === index ? (
                                 <ChevronUp className="w-5 h-5 text-muted-foreground" />
                              ) : (
                                 <ChevronDown className="w-5 h-5 text-muted-foreground" />
                              )}
                           </button>
                           {expandedFaq === index && (
                              <div className="px-4 pb-4">
                                 <p className="text-muted-foreground text-sm leading-relaxed">
                                    {faq.answer}
                                 </p>
                              </div>
                           )}
                        </div>
                     ))}
                  </div>
               </motion.div>
            </motion.div>
         </main>
      </div>
   );
}
