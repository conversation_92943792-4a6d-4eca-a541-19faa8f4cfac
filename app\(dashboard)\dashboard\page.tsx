"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AccountInfoModal } from "@/components/ui/modal";
import { SendMoneyModal } from "@/components/ui/send-money-modal";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import {
   ArrowDown,
   ArrowDownLeft,
   ArrowLeftRight,
   ArrowUpRight,
   CheckCircle,
   Coffee,
   CreditCard,
   Eye,
   EyeOff,
   Globe,
   HelpCircle,
   History,
   Home,
   Info,
   Plus,
   Receipt,
   Send,
   ShoppingCart,
   Smartphone,
   Target,
   TrendingUp,
   TrendingUp as TrendingUpIcon,
   Wallet,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function Dashboard() {
   const router = useRouter();
   const {
      isAuthenticated,
      isPinVerified,
      user: authUser,
      logout,
   } = useAuthStore();

   const [showBalance, setShowBalance] = useState(true);
   const [showAccountModal, setShowAccountModal] = useState(false);
   const [showSendMoneyModal, setShowSendMoneyModal] = useState(false);

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const user = authUser || {
      id: "USR-2024-001",
      firstName: "Deji",
      lastName: "Ade",
      username: "dejiade",
      email: "<EMAIL>",
      phoneNumber: "***********",
      country: "Nigeria",
      accountType: "Premium Savings",
   };

   // Extended user data for banking
   const bankingData = {
      accountNumber: "**********",
      routingNumber: "*********",
      accountStatus: "Active",
      availableBalance: 12847.52,
      accountType: "Premium Savings Account",
   };

   // Extended user data for account modal
   const extendedUserData = {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      accountNumber: bankingData.accountNumber,
      routingNumber: bankingData.routingNumber,
      accountType: bankingData.accountType,
      accountStatus: bankingData.accountStatus,
      dateOpened: "January 15, 2023",
      branch: "Paramount Bank - Downtown Branch, Lagos",
   };

   const getInitials = () => {
      return `${user.firstName.charAt(0)}${user.lastName.charAt(
         0
      )}`.toUpperCase();
   };

   // Mock transaction data
   const recentTransactions = [
      {
         id: "TXN-001",
         type: "debit",
         amount: 45.99,
         description: "Starbucks Coffee",
         merchant: "Starbucks",
         date: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
         icon: Coffee,
         category: "Food & Dining",
      },
      {
         id: "TXN-002",
         type: "credit",
         amount: 2500.0,
         description: "Salary Deposit",
         merchant: "Acme Corp",
         date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
         icon: ArrowDown,
         category: "Income",
      },
      {
         id: "TXN-003",
         type: "debit",
         amount: 89.5,
         description: "Grocery Shopping",
         merchant: "Walmart",
         date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
         icon: ShoppingCart,
         category: "Groceries",
      },
      {
         id: "TXN-004",
         type: "debit",
         amount: 1200.0,
         description: "Rent Payment",
         merchant: "Property Management",
         date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
         icon: Home,
         category: "Housing",
      },
      {
         id: "TXN-005",
         type: "debit",
         amount: 299.99,
         description: "iPhone Case",
         merchant: "Apple Store",
         date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
         icon: Smartphone,
         category: "Electronics",
      },
   ];

   const formatTransactionDate = (date: Date) => {
      const now = new Date();
      const diffInHours = Math.floor(
         (now.getTime() - date.getTime()) / (1000 * 60 * 60)
      );

      if (diffInHours < 24) {
         return diffInHours === 0 ? "Just now" : `${diffInHours}h ago`;
      } else {
         const diffInDays = Math.floor(diffInHours / 24);
         return diffInDays === 1 ? "Yesterday" : `${diffInDays} days ago`;
      }
   };

   // Mock notification data
   const notifications: Notification[] = [
      {
         id: "notif-1",
         title: "Payment Received",
         message: "You received $2,500.00 from Acme Corp",
         type: "success",
         timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
         read: false,
      },
      {
         id: "notif-2",
         title: "Card Transaction",
         message: "Purchase at Starbucks for $45.99",
         type: "info",
         timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
         read: false,
      },
      {
         id: "notif-3",
         title: "Security Alert",
         message: "New device login detected from Chrome on Windows",
         type: "warning",
         timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
         read: true,
      },
      {
         id: "notif-4",
         title: "Monthly Statement",
         message: "Your December statement is now available",
         type: "info",
         timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
         read: true,
      },
   ];

   // Enhanced user data for dropdown
   const userDropdownData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      accountType: user.accountType,
      verificationStatus: "verified" as const,
   };

   const handleMarkNotificationAsRead = (id: string) => {
      // In a real app, this would update the notification state
      console.log("Mark notification as read:", id);
   };

   const handleMarkAllNotificationsAsRead = () => {
      // In a real app, this would mark all notifications as read
      console.log("Mark all notifications as read");
   };

   const handleViewAllNotifications = () => {
      router.push("/dashboard/notifications");
   };

   const handleUserProfile = () => {
      router.push("/dashboard/profile");
   };

   const handleUserSupport = () => {
      router.push("/dashboard/support");
   };

   const handleLogout = () => {
      logout();
      router.push("/");
   };

   // Don't render if not authenticated or PIN not verified
   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="container mx-auto px-4 py-8">
         <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
         >
            {/* Welcome Section */}
            <div className="mb-8">
               <h1 className="text-3xl font-bold text-foreground mb-2">
                  Welcome back, {user?.firstName}!
               </h1>
               <p className="text-muted-foreground">
                  Here&apos;s an overview of your account activity.
               </p>
            </div>

            {/* Account Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
               <div className="bank-card p-6">
                  <div className="flex items-start justify-between mb-4">
                     <div>
                        <p className="text-sm text-muted-foreground">
                           Total Balance
                        </p>
                        <p className="text-2xl font-bold text-foreground">
                           $12,847.52
                        </p>
                     </div>
                     <div className="p-2 bg-success/10 rounded-lg">
                        <ArrowUpRight className="w-5 h-5 text-success" />
                     </div>
                  </div>
                  <p className="text-sm text-success">+2.5% from last month</p>
               </div>

               <div className="bank-card p-6">
                  <div className="flex items-start justify-between mb-4">
                     <div>
                        <p className="text-sm text-muted-foreground">
                           Monthly Spending
                        </p>
                        <p className="text-2xl font-bold text-foreground">
                           $2,143.89
                        </p>
                     </div>
                     <div className="p-2 bg-warning/10 rounded-lg">
                        <ArrowDownLeft className="w-5 h-5 text-warning" />
                     </div>
                  </div>
                  <p className="text-sm text-warning">+12% from last month</p>
               </div>

               <div className="bank-card p-6">
                  <div className="flex items-start justify-between mb-4">
                     <div>
                        <p className="text-sm text-muted-foreground">
                           Monthly Income
                        </p>
                        <p className="text-2xl font-bold text-foreground">
                           $4,250.00
                        </p>
                     </div>
                     <div className="p-2 bg-success/10 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-success" />
                     </div>
                  </div>
                  <p className="text-sm text-success">+8.2% from last month</p>
               </div>

               <div className="bank-card p-6">
                  <div className="flex items-start justify-between mb-4">
                     <div>
                        <p className="text-sm text-muted-foreground">
                           Transactions
                        </p>
                        <p className="text-2xl font-bold text-foreground">47</p>
                     </div>
                     <div className="p-2 bg-primary/10 rounded-lg">
                        <Receipt className="w-5 h-5 text-primary" />
                     </div>
                  </div>
                  <p className="text-sm text-muted-foreground">This month</p>
               </div>
            </div>

            {/* Modern Bank Balance Card */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
               <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-primary via-primary-light to-primary-dark text-primary-foreground p-6 shadow-lg">
                  {/* Header with Avatar and User Info */}
                  <div className="flex items-center justify-between mb-6">
                     <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12 border-2 border-primary-foreground/20">
                           <AvatarImage
                              src=""
                              alt={`${user.firstName} ${user.lastName}`}
                           />
                           <AvatarFallback className="bg-primary-foreground/10 text-primary-foreground font-semibold">
                              {getInitials()}
                           </AvatarFallback>
                        </Avatar>
                        <div>
                           <h3 className="font-semibold text-lg">
                              {user.firstName} {user.lastName}
                           </h3>
                           <p className="text-primary-foreground/80 text-sm">
                              ID: {user.id}
                           </p>
                        </div>
                     </div>
                     <div className="text-right">
                        <Badge
                           variant="secondary"
                           className="bg-primary-foreground/20 text-primary-foreground border-primary-foreground/30"
                        >
                           <CheckCircle className="w-3 h-3 mr-1" />
                           Verified
                        </Badge>
                     </div>
                  </div>

                  {/* Balance Section */}
                  <div className="mb-6">
                     <div className="flex items-center justify-between mb-2">
                        <p className="text-primary-foreground/80 text-sm">
                           Available Balance
                        </p>
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={() => setShowBalance(!showBalance)}
                           className="text-primary-foreground/80 hover:text-primary-foreground hover:bg-primary-foreground/10 h-6 w-6 p-0"
                        >
                           {showBalance ? (
                              <EyeOff className="h-4 w-4" />
                           ) : (
                              <Eye className="h-4 w-4" />
                           )}
                        </Button>
                     </div>
                     <p className="text-3xl font-bold">
                        {showBalance
                           ? `$${bankingData.availableBalance.toLocaleString(
                                "en-US",
                                { minimumFractionDigits: 2 }
                             )}`
                           : "••••••"}
                     </p>
                  </div>

                  {/* Account Info Bar */}
                  <div className="bg-primary-foreground/10 rounded-lg p-4 mb-6 backdrop-blur-sm">
                     <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                           <p className="text-primary-foreground/70">
                              Account No.
                           </p>
                           <p className="font-medium">
                              ****{bankingData.accountNumber.slice(-4)}
                           </p>
                        </div>
                        <div>
                           <p className="text-primary-foreground/70">Status</p>
                           <p className="font-medium">
                              {bankingData.accountStatus}
                           </p>
                        </div>
                        <div>
                           <p className="text-primary-foreground/70">Type</p>
                           <p className="font-medium">{user.accountType}</p>
                        </div>
                     </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-3">
                     <Link href="/dashboard/account-history">
                        <Button
                           variant="secondary"
                           className="bg-primary-foreground/20 text-primary-foreground border-primary-foreground/30 hover:bg-primary-foreground/30 w-full"
                        >
                           <History className="w-4 h-4 mr-2" />
                           Transactions
                        </Button>
                     </Link>
                     <Link href="/dashboard/deposit">
                        <Button
                           variant="secondary"
                           className="bg-primary-foreground/20 text-primary-foreground border-primary-foreground/30 hover:bg-primary-foreground/30 w-full"
                        >
                           <Plus className="w-4 h-4 mr-2" />
                           Top Up
                        </Button>
                     </Link>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-primary-foreground/5 rounded-full -translate-y-16 translate-x-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-primary-foreground/5 rounded-full translate-y-12 -translate-x-12"></div>
               </div>

               <div className="bank-card p-6">
                  <div className="flex items-center gap-3 mb-6">
                     <div className="p-2 bg-accent/10 rounded-lg">
                        <CreditCard className="w-5 h-5 text-accent" />
                     </div>
                     <h2 className="text-xl font-semibold text-foreground">
                        Quick Actions
                     </h2>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                     <Button
                        variant="outline"
                        className="h-auto p-4 flex-col gap-2 hover:bg-primary/5 hover:border-primary/20"
                        onClick={() => setShowAccountModal(true)}
                     >
                        <Info className="w-6 h-6 text-primary" />
                        <div className="text-center">
                           <p className="font-medium text-sm">Account Info</p>
                           <p className="text-xs text-muted-foreground">
                              View details
                           </p>
                        </div>
                     </Button>

                     <Button
                        variant="outline"
                        className="h-auto p-4 flex-col gap-2 hover:bg-success/5 hover:border-success/20"
                        onClick={() => setShowSendMoneyModal(true)}
                     >
                        <Send className="w-6 h-6 text-success" />
                        <div className="text-center">
                           <p className="font-medium text-sm">Send Money</p>
                           <p className="text-xs text-muted-foreground">
                              Transfer funds
                           </p>
                        </div>
                     </Button>

                     <Link href="/dashboard/deposit">
                        <Button
                           variant="outline"
                           className="h-auto p-4 flex-col gap-2 hover:bg-warning/5 hover:border-warning/20 w-full"
                        >
                           <Wallet className="w-6 h-6 text-warning" />
                           <div className="text-center">
                              <p className="font-medium text-sm">Deposit</p>
                              <p className="text-xs text-muted-foreground">
                                 Add funds
                              </p>
                           </div>
                        </Button>
                     </Link>

                     <Link href="/dashboard/account-history">
                        <Button
                           variant="outline"
                           className="h-auto p-4 flex-col gap-2 hover:bg-accent/5 hover:border-accent/20 w-full"
                        >
                           <History className="w-6 h-6 text-accent" />
                           <div className="text-center">
                              <p className="font-medium text-sm">History</p>
                              <p className="text-xs text-muted-foreground">
                                 View transactions
                              </p>
                           </div>
                        </Button>
                     </Link>
                  </div>
               </div>
            </div>

            {/* Quick Transfer Card */}
            <div className="mt-8">
               <div className="bank-card p-6">
                  <div className="flex items-center gap-3 mb-6">
                     <div className="p-2 bg-success/10 rounded-lg">
                        <Send className="w-5 h-5 text-success" />
                     </div>
                     <h2 className="text-xl font-semibold text-foreground">
                        Quick Transfer
                     </h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                     <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="border border-border rounded-lg p-4 cursor-pointer hover:border-primary/50 hover:bg-primary/5 transition-all"
                        onClick={() => router.push("/dashboard/local-transfer")}
                     >
                        <div className="flex items-center gap-4">
                           <div className="p-3 bg-primary/10 rounded-lg">
                              <ArrowLeftRight className="w-6 h-6 text-primary" />
                           </div>
                           <div>
                              <h3 className="font-semibold text-foreground">
                                 Local Transfer
                              </h3>
                              <p className="text-sm text-muted-foreground">
                                 Send money domestically
                              </p>
                              <p className="text-xs text-success mt-1">
                                 Instant • Free
                              </p>
                           </div>
                        </div>
                     </motion.div>

                     <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="border border-border rounded-lg p-4 cursor-pointer hover:border-success/50 hover:bg-success/5 transition-all"
                        onClick={() =>
                           router.push("/dashboard/international-transfer")
                        }
                     >
                        <div className="flex items-center gap-4">
                           <div className="p-3 bg-success/10 rounded-lg">
                              <Globe className="w-6 h-6 text-success" />
                           </div>
                           <div>
                              <h3 className="font-semibold text-foreground">
                                 International Wire
                              </h3>
                              <p className="text-sm text-muted-foreground">
                                 Send money globally
                              </p>
                              <p className="text-xs text-warning mt-1">
                                 1-3 days • $15-25 fee
                              </p>
                           </div>
                        </div>
                     </motion.div>
                  </div>
               </div>
            </div>

            {/* Support Card */}
            <div className="mt-8">
               <div className="bank-card p-6">
                  <div className="flex items-center justify-between">
                     <div className="flex items-center gap-4">
                        <div className="p-3 bg-accent/10 rounded-lg">
                           <HelpCircle className="w-8 h-8 text-accent" />
                        </div>
                        <div>
                           <h3 className="text-xl font-semibold text-foreground">
                              Need Help?
                           </h3>
                           <p className="text-muted-foreground">
                              Our support team is here to assist you 24/7
                           </p>
                        </div>
                     </div>
                     <Button
                        variant="premium"
                        onClick={() => router.push("/dashboard/support")}
                        className="flex-shrink-0"
                     >
                        <HelpCircle className="w-4 h-4 mr-2" />
                        Contact Support
                     </Button>
                  </div>
               </div>
            </div>

            {/* Recent Transactions */}
            <div className="mt-8">
               <div className="bank-card p-6">
                  <div className="flex items-center justify-between mb-6">
                     <h2 className="text-xl font-semibold text-foreground">
                        Recent Transactions
                     </h2>
                     <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                           router.push("/dashboard/account-history")
                        }
                     >
                        View All
                     </Button>
                  </div>

                  <div className="space-y-4">
                     {recentTransactions.map((transaction) => {
                        const IconComponent = transaction.icon;
                        return (
                           <div
                              key={transaction.id}
                              className="flex items-center justify-between p-4 rounded-lg border border-border hover:bg-muted/30 transition-colors"
                           >
                              <div className="flex items-center gap-4">
                                 <div
                                    className={`p-2 rounded-lg ${
                                       transaction.type === "credit"
                                          ? "bg-success/10"
                                          : "bg-muted"
                                    }`}
                                 >
                                    <IconComponent
                                       className={`w-5 h-5 ${
                                          transaction.type === "credit"
                                             ? "text-success"
                                             : "text-muted-foreground"
                                       }`}
                                    />
                                 </div>
                                 <div>
                                    <p className="font-medium text-foreground">
                                       {transaction.description}
                                    </p>
                                    <p className="text-sm text-muted-foreground">
                                       {transaction.merchant} •{" "}
                                       {transaction.category}
                                    </p>
                                 </div>
                              </div>
                              <div className="text-right">
                                 <p
                                    className={`font-semibold ${
                                       transaction.type === "credit"
                                          ? "text-success"
                                          : "text-foreground"
                                    }`}
                                 >
                                    {transaction.type === "credit" ? "+" : "-"}$
                                    {transaction.amount.toLocaleString(
                                       "en-US",
                                       { minimumFractionDigits: 2 }
                                    )}
                                 </p>
                                 <p className="text-sm text-muted-foreground">
                                    {formatTransactionDate(transaction.date)}
                                 </p>
                              </div>
                           </div>
                        );
                     })}
                  </div>
               </div>
            </div>

            {/* Your Cards Section */}
            <div className="mt-8">
               <div className="bank-card p-6">
                  <div className="flex items-center justify-between">
                     <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-primary/10 rounded-lg">
                           <CreditCard className="w-5 h-5 text-primary" />
                        </div>
                        <h2 className="text-xl font-semibold text-foreground">
                           Your Cards
                        </h2>
                     </div>
                     <Link href="/dashboard/cards">
                        <Button
                           variant="premium"
                           className="inline-flex items-center gap-2"
                        >
                           <Plus className="w-4 h-4" />
                           Go to Cards
                        </Button>
                     </Link>
                  </div>

                  {/* Empty State */}
                  <div className="text-center py-12">
                     <div className="mx-auto w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                        <CreditCard className="w-8 h-8 text-muted-foreground" />
                     </div>
                     <h3 className="text-lg font-semibold text-foreground mb-2">
                        No Cards Yet
                     </h3>
                     <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                        Apply for a Paramount Bank card to enjoy exclusive
                        benefits, rewards, and secure payments worldwide.
                     </p>
                     <Button
                        variant="premium"
                        onClick={() => router.push("/dashboard/cards/apply")}
                        className="inline-flex items-center gap-2"
                     >
                        <Plus className="w-4 h-4" />
                        Apply for Card
                     </Button>
                  </div>
               </div>
            </div>

            {/* Additional Banking Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
               {/* Savings Goals Card */}
               <div className="bank-card p-6">
                  <div className="flex items-center gap-3 mb-6">
                     <div className="p-2 bg-success/10 rounded-lg">
                        <Target className="w-5 h-5 text-success" />
                     </div>
                     <h2 className="text-xl font-semibold text-foreground">
                        Savings Goals
                     </h2>
                  </div>

                  <div className="space-y-4">
                     <div className="p-4 bg-muted/30 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                           <h3 className="font-medium text-foreground">
                              Emergency Fund
                           </h3>
                           <span className="text-sm text-success font-medium">
                              78%
                           </span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2 mb-2">
                           <div
                              className="bg-success h-2 rounded-full"
                              style={{ width: "78%" }}
                           ></div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                           $7,800 of $10,000 goal
                        </p>
                     </div>

                     <div className="p-4 bg-muted/30 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                           <h3 className="font-medium text-foreground">
                              Vacation Fund
                           </h3>
                           <span className="text-sm text-warning font-medium">
                              45%
                           </span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2 mb-2">
                           <div
                              className="bg-warning h-2 rounded-full"
                              style={{ width: "45%" }}
                           ></div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                           $2,250 of $5,000 goal
                        </p>
                     </div>

                     <Button variant="outline" className="w-full">
                        <Plus className="w-4 h-4 mr-2" />
                        Add New Goal
                     </Button>
                  </div>
               </div>

               {/* Investment Summary Card */}
               <div className="bank-card p-6">
                  <div className="flex items-center gap-3 mb-6">
                     <div className="p-2 bg-accent/10 rounded-lg">
                        <TrendingUpIcon className="w-5 h-5 text-accent" />
                     </div>
                     <h2 className="text-xl font-semibold text-foreground">
                        Investment Portfolio
                     </h2>
                  </div>

                  <div className="space-y-4">
                     <div className="text-center p-4 bg-gradient-to-br from-accent/5 to-accent/10 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">
                           Total Portfolio Value
                        </p>
                        <p className="text-2xl font-bold text-foreground">
                           $25,847.32
                        </p>
                        <p className="text-sm text-success">
                           +12.5% this month
                        </p>
                     </div>

                     <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                           <p className="text-xs text-muted-foreground">
                              Stocks
                           </p>
                           <p className="font-semibold text-foreground">
                              $18,500
                           </p>
                           <p className="text-xs text-success">+8.2%</p>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                           <p className="text-xs text-muted-foreground">
                              Bonds
                           </p>
                           <p className="font-semibold text-foreground">
                              $7,347
                           </p>
                           <p className="text-xs text-success">+3.1%</p>
                        </div>
                     </div>

                     <Button variant="outline" className="w-full">
                        <TrendingUpIcon className="w-4 h-4 mr-2" />
                        View Portfolio
                     </Button>
                  </div>
               </div>
            </div>
         </motion.div>

         {/* Modals */}
         <AccountInfoModal
            isOpen={showAccountModal}
            onClose={() => setShowAccountModal(false)}
            user={extendedUserData}
         />

         <SendMoneyModal
            isOpen={showSendMoneyModal}
            onClose={() => setShowSendMoneyModal(false)}
            onLocalTransfer={() => router.push("/dashboard/local-transfer")}
            onInternationalTransfer={() =>
               router.push("/dashboard/international-transfer")
            }
         />
      </div>
   );
}
