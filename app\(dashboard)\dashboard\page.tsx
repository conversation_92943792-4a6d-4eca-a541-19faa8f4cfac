"use client";

import { BankLogo } from "@/components/bank-logo";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
   ArrowDownLeft,
   ArrowUpRight,
   CreditCard,
   LogOut,
   <PERSON><PERSON><PERSON>,
   User,
} from "lucide-react";
import { useRouter } from "next/navigation";

export default function Dashboard() {
   const user = {
      firstName: "Deji",
      lastName: "Ade",
      username: "dejiade",
      email: "<EMAIL>",
      phoneNumber: "***********",
      country: "Nigeria",
      accountType: "Savings",
   };

   const router = useRouter();

   const logout = () => {
      alert("Logout");
   };

   const handleLogout = () => {
      logout();
      router.push("/");
   };

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center justify-between">
                  <BankLogo size="sm" />

                  <div className="flex items-center gap-4">
                     <div className="text-right">
                        <p className="font-medium text-foreground">
                           {user?.firstName} {user?.lastName}
                        </p>
                        <p className="text-sm text-muted-foreground">
                           {user?.accountType}
                        </p>
                     </div>
                     <Button
                        variant="outline"
                        size="sm"
                        onClick={handleLogout}
                        className="flex items-center gap-2"
                     >
                        <LogOut size={16} />
                        Sign Out
                     </Button>
                  </div>
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
            >
               {/* Welcome Section */}
               <div className="mb-8">
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Welcome back, {user?.firstName}!
                  </h1>
                  <p className="text-muted-foreground">
                     Here&apos;s an overview of your account activity.
                  </p>
               </div>

               {/* Account Overview */}
               <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bank-card p-6">
                     <div className="flex items-start justify-between mb-4">
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Total Balance
                           </p>
                           <p className="text-2xl font-bold text-foreground">
                              $12,847.52
                           </p>
                        </div>
                        <div className="p-2 bg-success/10 rounded-lg">
                           <ArrowUpRight className="w-5 h-5 text-success" />
                        </div>
                     </div>
                     <p className="text-sm text-success">
                        +2.5% from last month
                     </p>
                  </div>

                  <div className="bank-card p-6">
                     <div className="flex items-start justify-between mb-4">
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Monthly Spending
                           </p>
                           <p className="text-2xl font-bold text-foreground">
                              $2,143.89
                           </p>
                        </div>
                        <div className="p-2 bg-warning/10 rounded-lg">
                           <ArrowDownLeft className="w-5 h-5 text-warning" />
                        </div>
                     </div>
                     <p className="text-sm text-warning">
                        +12% from last month
                     </p>
                  </div>

                  <div className="bank-card p-6">
                     <div className="flex items-start justify-between mb-4">
                        <div>
                           <p className="text-sm text-muted-foreground">
                              Savings Goal
                           </p>
                           <p className="text-2xl font-bold text-foreground">
                              78%
                           </p>
                        </div>
                        <div className="p-2 bg-primary/10 rounded-lg">
                           <PieChart className="w-5 h-5 text-primary" />
                        </div>
                     </div>
                     <p className="text-sm text-muted-foreground">
                        $7,800 of $10,000
                     </p>
                  </div>
               </div>

               {/* Account Information */}
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="bank-card p-6">
                     <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-primary/10 rounded-lg">
                           <User className="w-5 h-5 text-primary" />
                        </div>
                        <h2 className="text-xl font-semibold text-foreground">
                           Account Information
                        </h2>
                     </div>

                     <div className="space-y-4">
                        <div className="flex justify-between">
                           <span className="text-muted-foreground">
                              Full Name
                           </span>
                           <span className="font-medium text-foreground">
                              {user?.firstName} {user?.lastName}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-muted-foreground">
                              Username
                           </span>
                           <span className="font-medium text-foreground">
                              {user?.username}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-muted-foreground">Email</span>
                           <span className="font-medium text-foreground">
                              {user?.email}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-muted-foreground">Phone</span>
                           <span className="font-medium text-foreground">
                              {user?.phoneNumber}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-muted-foreground">
                              Country
                           </span>
                           <span className="font-medium text-foreground">
                              {user?.country}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-muted-foreground">
                              Account Type
                           </span>
                           <span className="font-medium text-foreground">
                              {user?.accountType}
                           </span>
                        </div>
                     </div>
                  </div>

                  <div className="bank-card p-6">
                     <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-accent/10 rounded-lg">
                           <CreditCard className="w-5 h-5 text-accent" />
                        </div>
                        <h2 className="text-xl font-semibold text-foreground">
                           Quick Actions
                        </h2>
                     </div>

                     <div className="space-y-3">
                        <Button
                           variant="outline"
                           className="w-full justify-start"
                        >
                           Transfer Money
                        </Button>
                        <Button
                           variant="outline"
                           className="w-full justify-start"
                        >
                           Pay Bills
                        </Button>
                        <Button
                           variant="outline"
                           className="w-full justify-start"
                        >
                           View Statements
                        </Button>
                        <Button
                           variant="outline"
                           className="w-full justify-start"
                        >
                           Manage Cards
                        </Button>
                        <Button
                           variant="outline"
                           className="w-full justify-start"
                        >
                           Contact Support
                        </Button>
                     </div>
                  </div>
               </div>

               {/* Recent Transactions Placeholder */}
               <div className="mt-8">
                  <div className="bank-card p-6">
                     <h2 className="text-xl font-semibold text-foreground mb-4">
                        Recent Transactions
                     </h2>
                     <div className="text-center py-8">
                        <p className="text-muted-foreground">
                           Transaction history will appear here
                        </p>
                     </div>
                  </div>
               </div>
            </motion.div>
         </main>
      </div>
   );
}
