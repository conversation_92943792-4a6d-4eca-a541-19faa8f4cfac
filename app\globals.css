@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
   /* Paramount Bank Color Palette - Trustworthy navy blues with gold accents */
   --color-background: hsl(0 0% 100%);
   --color-foreground: hsl(216 82% 8%);
   --color-card: hsl(0 0% 100%);
   --color-card-foreground: hsl(216 82% 8%);
   --color-popover: hsl(0 0% 100%);
   --color-popover-foreground: hsl(216 82% 8%);

   /* Primary: Deep navy blue for trust and stability */
   --color-primary: hsl(217 91% 14%);
   --color-primary-foreground: hsl(0 0% 98%);
   --color-primary-light: hsl(217 82% 24%);
   --color-primary-dark: hsl(217 95% 8%);

   /* Secondary: Soft gray-blue for supporting elements */
   --color-secondary: hsl(218 15% 96%);
   --color-secondary-foreground: hsl(217 32% 17%);

   /* Gold accent for premium banking feel */
   --color-accent: hsl(45 93% 58%);
   --color-accent-foreground: hsl(217 91% 14%);
   --color-accent-light: hsl(45 93% 68%);
   --color-accent-dark: hsl(45 93% 48%);

   /* Muted colors for subtle UI elements */
   --color-muted: hsl(218 15% 96%);
   --color-muted-foreground: hsl(215 16% 47%);

   /* Success green for positive actions */
   --color-success: hsl(142 69% 50%);
   --color-success-foreground: hsl(0 0% 98%);

   /* Warning amber for caution */
   --color-warning: hsl(32 95% 44%);
   --color-warning-foreground: hsl(0 0% 98%);

   /* Destructive red for errors and dangerous actions */
   --color-destructive: hsl(0 84% 60%);
   --color-destructive-foreground: hsl(0 0% 98%);

   /* Border and input styling */
   --color-border: hsl(220 13% 91%);
   --color-input: hsl(220 13% 91%);
   --color-ring: hsl(217 91% 14%);

   /* Sidebar colors */
   --color-sidebar: hsl(0 0% 98%);
   --color-sidebar-foreground: hsl(240 5.3% 26.1%);
   --color-sidebar-primary: hsl(240 5.9% 10%);
   --color-sidebar-primary-foreground: hsl(0 0% 98%);
   --color-sidebar-accent: hsl(240 4.8% 95.9%);
   --color-sidebar-accent-foreground: hsl(240 5.9% 10%);
   --color-sidebar-border: hsl(220 13% 91%);
   --color-sidebar-ring: hsl(217.2 91.2% 59.8%);

   /* Chart colors */
   --color-chart-1: hsl(45 93% 58%);
   --color-chart-2: hsl(217 91% 14%);
   --color-chart-3: hsl(142 69% 50%);
   --color-chart-4: hsl(32 95% 44%);
   --color-chart-5: hsl(0 84% 60%);

   /* Font families */
   --font-sans: var(--font-geist-sans);
   --font-mono: var(--font-geist-mono);

   /* Border radius */
   --radius-sm: calc(0.75rem - 4px);
   --radius-md: calc(0.75rem - 2px);
   --radius-lg: 0.75rem;
   --radius-xl: calc(0.75rem + 4px);
}

:root {
   /* Banking-specific gradients */
   --gradient-primary: linear-gradient(
      135deg,
      hsl(217 91% 14%),
      hsl(217 82% 24%)
   );
   --gradient-accent: linear-gradient(135deg, hsl(45 93% 58%), hsl(45 93% 68%));
   --gradient-hero: linear-gradient(135deg, hsl(217 95% 8%), hsl(217 91% 14%));
   --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(218 15% 98%));

   /* Premium shadows */
   --shadow-card: 0 4px 20px -4px hsl(217 91% 14% / 0.1);
   --shadow-button: 0 2px 10px -2px hsl(217 91% 14% / 0.2);
   --shadow-floating: 0 10px 40px -10px hsl(217 91% 14% / 0.15);

   /* Smooth transitions */
   --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
   --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.dark {
   /* Dark mode with elegant dark navy and gold accents */
   --color-background: hsl(217 95% 8%);
   --color-foreground: hsl(0 0% 98%);
   --color-card: hsl(217 82% 12%);
   --color-card-foreground: hsl(0 0% 98%);
   --color-popover: hsl(217 82% 12%);
   --color-popover-foreground: hsl(0 0% 98%);
   --color-primary: hsl(45 93% 58%);
   --color-primary-foreground: hsl(217 95% 8%);
   --color-secondary: hsl(217 32% 20%);
   --color-secondary-foreground: hsl(0 0% 98%);
   --color-accent: hsl(45 93% 58%);
   --color-accent-foreground: hsl(217 95% 8%);
   --color-muted: hsl(217 32% 20%);
   --color-muted-foreground: hsl(215 20% 65%);
   --color-destructive: hsl(0 84% 60%);
   --color-border: hsl(217 32% 20%);
   --color-input: hsl(217 32% 20%);
   --color-ring: hsl(45 93% 58%);
   --color-sidebar: hsl(217 82% 12%);
   --color-sidebar-foreground: hsl(0 0% 98%);
   --color-sidebar-primary: hsl(45 93% 58%);
   --color-sidebar-primary-foreground: hsl(217 95% 8%);
   --color-sidebar-accent: hsl(217 32% 20%);
   --color-sidebar-accent-foreground: hsl(0 0% 98%);
   --color-sidebar-border: hsl(217 32% 20%);
   --color-sidebar-ring: hsl(45 93% 58%);

   /* Dark mode gradients */
   --gradient-primary: linear-gradient(
      135deg,
      hsl(45 93% 58%),
      hsl(45 93% 68%)
   );
   --gradient-hero: linear-gradient(135deg, hsl(217 95% 8%), hsl(217 82% 12%));
   --gradient-card: linear-gradient(145deg, hsl(217 82% 12%), hsl(217 32% 20%));
}

@layer base {
   * {
      @apply border-border outline-ring/50;
   }
   body {
      @apply bg-background text-foreground font-sans antialiased;
   }
}

/* Banking-specific component classes */
@layer components {
   .container {
      @apply max-w-3xl mx-auto px-4 sm:px-6 lg:px-8;
   }

   .bank-card {
      @apply bg-gradient-to-br from-card to-secondary border border-border rounded-xl transition-all duration-300 ease-out;
      box-shadow: var(--shadow-card);
   }

   .bank-button-primary {
      @apply text-primary-foreground shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 ease-out;
      background: var(--gradient-primary);
      box-shadow: var(--shadow-button);
   }

   .bank-button-accent {
      @apply text-accent-foreground shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 ease-out;
      background: var(--gradient-accent);
      box-shadow: var(--shadow-button);
   }

   .bank-input {
      @apply border-2 border-border bg-background/50 backdrop-blur-sm focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300 ease-out;
   }

   .bank-progress-step {
      @apply w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium transition-all duration-300 ease-out;
   }

   .bank-progress-step.active {
      @apply bg-primary border-primary text-primary-foreground;
   }

   .bank-progress-step.completed {
      @apply bg-success border-success text-success-foreground;
   }

   .bank-progress-step.inactive {
      @apply bg-muted border-border text-muted-foreground;
   }
}

.container {
   @apply container mx-auto p-8;
}

@media (min-width: 1400px) {
   .container {
      max-width: 1400px;
   }
}

/* Custom animations */
@keyframes slide-in-right {
   from {
      opacity: 0;
      transform: translateX(100%);
   }
   to {
      opacity: 1;
      transform: translateX(0);
   }
}

@keyframes slide-out-left {
   from {
      opacity: 1;
      transform: translateX(0);
   }
   to {
      opacity: 0;
      transform: translateX(-100%);
   }
}

@keyframes fade-in-up {
   from {
      opacity: 0;
      transform: translateY(20px);
   }
   to {
      opacity: 1;
      transform: translateY(0);
   }
}

@layer utilities {
   .animate-slide-in-right {
      animation: slide-in-right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
   }

   .animate-slide-out-left {
      animation: slide-out-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
   }

   .animate-fade-in-up {
      animation: fade-in-up 0.5s cubic-bezier(0.4, 0, 0.2, 1);
   }
}
