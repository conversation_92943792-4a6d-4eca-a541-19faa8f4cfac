"use client";

import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { X, Copy, Check } from "lucide-react";
import * as React from "react";
import { Button } from "./button";
import { Separator } from "./separator";

export interface ModalProps {
   isOpen: boolean;
   onClose: () => void;
   title: string;
   children: React.ReactNode;
   className?: string;
   size?: "sm" | "md" | "lg" | "xl";
}

const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
   ({ isOpen, onClose, title, children, className, size = "md" }, ref) => {
      const sizeClasses = {
         sm: "max-w-md",
         md: "max-w-lg",
         lg: "max-w-2xl",
         xl: "max-w-4xl",
      };

      React.useEffect(() => {
         const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape") onClose();
         };

         if (isOpen) {
            document.addEventListener("keydown", handleEscape);
            document.body.style.overflow = "hidden";
         }

         return () => {
            document.removeEventListener("keydown", handleEscape);
            document.body.style.overflow = "unset";
         };
      }, [isOpen, onClose]);

      return (
         <AnimatePresence>
            {isOpen && (
               <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                  <motion.div
                     initial={{ opacity: 0 }}
                     animate={{ opacity: 1 }}
                     exit={{ opacity: 0 }}
                     className="fixed inset-0 bg-black/50 backdrop-blur-sm"
                     onClick={onClose}
                  />
                  <motion.div
                     ref={ref}
                     initial={{ opacity: 0, scale: 0.95, y: 20 }}
                     animate={{ opacity: 1, scale: 1, y: 0 }}
                     exit={{ opacity: 0, scale: 0.95, y: 20 }}
                     transition={{ duration: 0.2 }}
                     className={cn(
                        "relative w-full bg-background border border-border rounded-lg shadow-lg",
                        sizeClasses[size],
                        className
                     )}
                  >
                     <div className="flex items-center justify-between p-6 border-b border-border">
                        <h2 className="text-lg font-semibold text-foreground">
                           {title}
                        </h2>
                        <Button
                           variant="ghost"
                           size="icon"
                           onClick={onClose}
                           className="h-8 w-8"
                        >
                           <X className="h-4 w-4" />
                        </Button>
                     </div>
                     <div className="p-6">{children}</div>
                  </motion.div>
               </div>
            )}
         </AnimatePresence>
      );
   }
);

Modal.displayName = "Modal";

// Copyable Field Component for account information
export interface CopyableFieldProps {
   label: string;
   value: string;
   className?: string;
   masked?: boolean;
}

const CopyableField = React.forwardRef<HTMLDivElement, CopyableFieldProps>(
   ({ label, value, className, masked = false }, ref) => {
      const [copied, setCopied] = React.useState(false);
      const [showValue, setShowValue] = React.useState(!masked);

      const handleCopy = async () => {
         try {
            await navigator.clipboard.writeText(value);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
         } catch (err) {
            console.error("Failed to copy:", err);
         }
      };

      const displayValue = masked && !showValue 
         ? "•".repeat(value.length) 
         : value;

      return (
         <div className={cn("space-y-2", className)} ref={ref}>
            <label className="text-sm font-medium text-muted-foreground">
               {label}
            </label>
            <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg border">
               <span 
                  className="flex-1 font-mono text-sm text-foreground cursor-pointer"
                  onClick={() => masked && setShowValue(!showValue)}
               >
                  {displayValue}
               </span>
               <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleCopy}
                  className="h-8 w-8 flex-shrink-0"
               >
                  {copied ? (
                     <Check className="h-4 w-4 text-success" />
                  ) : (
                     <Copy className="h-4 w-4" />
                  )}
               </Button>
            </div>
         </div>
      );
   }
);

CopyableField.displayName = "CopyableField";

// Account Information Modal
export interface AccountInfoModalProps {
   isOpen: boolean;
   onClose: () => void;
   user: {
      firstName: string;
      lastName: string;
      email: string;
      phoneNumber: string;
      accountNumber: string;
      routingNumber: string;
      accountType: string;
      accountStatus: string;
      dateOpened: string;
      branch: string;
   };
}

const AccountInfoModal = React.forwardRef<HTMLDivElement, AccountInfoModalProps>(
   ({ isOpen, onClose, user }, ref) => {
      return (
         <Modal
            isOpen={isOpen}
            onClose={onClose}
            title="Account Information"
            size="lg"
            ref={ref}
         >
            <div className="space-y-6">
               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <CopyableField label="Full Name" value={`${user.firstName} ${user.lastName}`} />
                  <CopyableField label="Email Address" value={user.email} />
                  <CopyableField label="Phone Number" value={user.phoneNumber} />
                  <CopyableField label="Account Type" value={user.accountType} />
               </div>

               <Separator />

               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <CopyableField 
                     label="Account Number" 
                     value={user.accountNumber} 
                     masked={true}
                  />
                  <CopyableField 
                     label="Routing Number" 
                     value={user.routingNumber}
                  />
                  <CopyableField label="Account Status" value={user.accountStatus} />
                  <CopyableField label="Date Opened" value={user.dateOpened} />
               </div>

               <Separator />

               <CopyableField label="Branch" value={user.branch} />

               <div className="flex justify-end pt-4">
                  <Button onClick={onClose} variant="outline">
                     Close
                  </Button>
               </div>
            </div>
         </Modal>
      );
   }
);

AccountInfoModal.displayName = "AccountInfoModal";

export { Modal, CopyableField, AccountInfoModal };
