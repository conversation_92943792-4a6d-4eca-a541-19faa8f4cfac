"use client";

import { cn } from "@/lib/utils";
import * as React from "react";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";

export interface CustomSelectProps {
   label?: string;
   error?: string;
   icon?: React.ReactNode;
   placeholder?: string;
   value?: string;
   onValueChange?: (value: string) => void;
   children?: React.ReactNode;
   disabled?: boolean;
   className?: string;
}

const CustomSelect = React.forwardRef<
   React.ElementRef<typeof SelectTrigger>,
   CustomSelectProps
>(
   (
      {
         className,
         label,
         error,
         icon,
         placeholder,
         value,
         onValueChange,
         children,
         disabled,
         ...props
      },
      ref
   ) => {
      return (
         <div className="space-y-2">
            {label && (
               <label className="text-sm font-medium text-foreground">
                  {label}
               </label>
            )}
            <div className="relative">
               {icon && (
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground z-10">
                     {icon}
                  </div>
               )}
               <Select
                  value={value}
                  onValueChange={onValueChange}
                  disabled={disabled}
               >
                  <SelectTrigger
                     ref={ref}
                     className={cn(
                        "bank-input flex h-12 w-full rounded-lg px-3 py-2 text-sm ring-offset-background",
                        icon && "pl-10",
                        error &&
                           "border-destructive focus:border-destructive focus:ring-destructive/20",
                        className
                     )}
                     {...props}
                  >
                     <SelectValue placeholder={placeholder} />
                  </SelectTrigger>
                  <SelectContent>{children}</SelectContent>
               </Select>
            </div>
            {error && <p className="text-sm text-destructive">{error}</p>}
         </div>
      );
   }
);

CustomSelect.displayName = "CustomSelect";

export { CustomSelect, SelectItem };
