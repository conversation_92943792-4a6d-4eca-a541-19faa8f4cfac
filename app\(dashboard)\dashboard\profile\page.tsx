"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/bank-logo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { ArrowLeft, User, Mail, Phone, MapPin, Shield, Camera } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function Profile() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified, user: authUser } = useAuthStore();
   const [isEditing, setIsEditing] = useState(false);
   const [formData, setFormData] = useState({
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      country: ""
   });

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   // Initialize form data
   useEffect(() => {
      if (authUser) {
         setFormData({
            firstName: authUser.firstName || "",
            lastName: authUser.lastName || "",
            email: authUser.email || "",
            phoneNumber: authUser.phoneNumber || "",
            address: "123 Main Street",
            city: "New York",
            state: "NY",
            zipCode: "10001",
            country: authUser.country || "United States"
         });
      }
   }, [authUser]);

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({
         ...prev,
         [name]: value
      }));
   };

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      // Handle profile update logic
      alert("Profile updated successfully!");
      setIsEditing(false);
   };

   const handleCancel = () => {
      // Reset form data
      if (authUser) {
         setFormData({
            firstName: authUser.firstName || "",
            lastName: authUser.lastName || "",
            email: authUser.email || "",
            phoneNumber: authUser.phoneNumber || "",
            address: "123 Main Street",
            city: "New York",
            state: "NY",
            zipCode: "10001",
            country: authUser.country || "United States"
         });
      }
      setIsEditing(false);
   };

   const getInitials = () => {
      if (!authUser) return "U";
      return `${authUser.firstName.charAt(0)}${authUser.lastName.charAt(0)}`.toUpperCase();
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center gap-4">
                  <Button
                     variant="ghost"
                     size="sm"
                     onClick={() => router.push("/dashboard")}
                     className="flex items-center gap-2"
                  >
                     <ArrowLeft size={16} />
                     Back to Dashboard
                  </Button>
                  <BankLogo size="sm" />
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-4xl mx-auto"
            >
               <div className="flex items-center justify-between mb-8">
                  <div>
                     <h1 className="text-3xl font-bold text-foreground mb-2">
                        Profile Settings
                     </h1>
                     <p className="text-muted-foreground">
                        Manage your personal information and account preferences
                     </p>
                  </div>
                  {!isEditing && (
                     <Button
                        variant="premium"
                        onClick={() => setIsEditing(true)}
                        className="flex items-center gap-2"
                     >
                        <User size={16} />
                        Edit Profile
                     </Button>
                  )}
               </div>

               <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Profile Picture and Basic Info */}
                  <div className="lg:col-span-1">
                     <div className="bank-card p-6 text-center">
                        <div className="relative inline-block mb-4">
                           <Avatar className="h-24 w-24">
                              <AvatarImage src="" alt={`${formData.firstName} ${formData.lastName}`} />
                              <AvatarFallback className="bg-primary text-primary-foreground text-xl">
                                 {getInitials()}
                              </AvatarFallback>
                           </Avatar>
                           {isEditing && (
                              <Button
                                 variant="outline"
                                 size="sm"
                                 className="absolute -bottom-2 -right-2 h-8 w-8 p-0 rounded-full"
                              >
                                 <Camera className="w-4 h-4" />
                              </Button>
                           )}
                        </div>
                        <h2 className="text-xl font-semibold text-foreground mb-1">
                           {formData.firstName} {formData.lastName}
                        </h2>
                        <p className="text-muted-foreground mb-4">
                           Premium Account Holder
                        </p>
                        <div className="flex items-center justify-center gap-2 text-success">
                           <Shield className="w-4 h-4" />
                           <span className="text-sm">Verified Account</span>
                        </div>
                     </div>

                     {/* Account Security */}
                     <div className="bank-card p-6 mt-6">
                        <h3 className="font-semibold text-foreground mb-4">Account Security</h3>
                        <div className="space-y-3">
                           <Button variant="outline" className="w-full justify-start">
                              Change Password
                           </Button>
                           <Button variant="outline" className="w-full justify-start">
                              Update PIN
                           </Button>
                           <Button variant="outline" className="w-full justify-start">
                              Two-Factor Authentication
                           </Button>
                        </div>
                     </div>
                  </div>

                  {/* Profile Form */}
                  <div className="lg:col-span-2">
                     <div className="bank-card p-6">
                        <form onSubmit={handleSubmit} className="space-y-6">
                           {/* Personal Information */}
                           <div>
                              <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                                 <User className="w-5 h-5" />
                                 Personal Information
                              </h3>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                 <Input
                                    label="First Name"
                                    name="firstName"
                                    value={formData.firstName}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    required
                                 />
                                 <Input
                                    label="Last Name"
                                    name="lastName"
                                    value={formData.lastName}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    required
                                 />
                              </div>
                           </div>

                           {/* Contact Information */}
                           <div>
                              <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                                 <Mail className="w-5 h-5" />
                                 Contact Information
                              </h3>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                 <Input
                                    label="Email Address"
                                    name="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    required
                                 />
                                 <Input
                                    label="Phone Number"
                                    name="phoneNumber"
                                    value={formData.phoneNumber}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    required
                                 />
                              </div>
                           </div>

                           {/* Address Information */}
                           <div>
                              <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                                 <MapPin className="w-5 h-5" />
                                 Address Information
                              </h3>
                              <div className="space-y-4">
                                 <Input
                                    label="Street Address"
                                    name="address"
                                    value={formData.address}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    required
                                 />
                                 <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <Input
                                       label="City"
                                       name="city"
                                       value={formData.city}
                                       onChange={handleInputChange}
                                       disabled={!isEditing}
                                       required
                                    />
                                    <Input
                                       label="State"
                                       name="state"
                                       value={formData.state}
                                       onChange={handleInputChange}
                                       disabled={!isEditing}
                                       required
                                    />
                                    <Input
                                       label="ZIP Code"
                                       name="zipCode"
                                       value={formData.zipCode}
                                       onChange={handleInputChange}
                                       disabled={!isEditing}
                                       required
                                    />
                                 </div>
                                 <Input
                                    label="Country"
                                    name="country"
                                    value={formData.country}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    required
                                 />
                              </div>
                           </div>

                           {/* Action Buttons */}
                           {isEditing && (
                              <div className="flex gap-4 pt-4">
                                 <Button
                                    type="submit"
                                    variant="premium"
                                    className="flex-1"
                                 >
                                    Save Changes
                                 </Button>
                                 <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    className="flex-1"
                                 >
                                    Cancel
                                 </Button>
                              </div>
                           )}
                        </form>
                     </div>
                  </div>
               </div>
            </motion.div>
         </main>
      </div>
   );
}
