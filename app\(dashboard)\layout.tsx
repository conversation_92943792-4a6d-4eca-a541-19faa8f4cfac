"use client";

import { BankLogo } from "@/components/bank-logo";
import { NotificationDropdown } from "@/components/ui/notification";
import { UserDropdown } from "@/components/ui/user-dropdown";
import { useAuthStore } from "@/stores/auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function DashboardLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.replace("/login");
      } else if (!isPinVerified) {
         router.replace("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border sticky top-0 z-40">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center justify-between">
                  {/* Logo */}
                  <BankLogo />

                  {/* Right side - Notifications and User */}
                  <div className="flex items-center gap-4">
                     <NotificationDropdown />
                     <UserDropdown />
                  </div>
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main>{children}</main>
      </div>
   );
}
