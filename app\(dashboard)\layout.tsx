"use client";

import { BankLogo } from "@/components/bank-logo";
import {
   NotificationDropdown,
   type Notification,
} from "@/components/ui/notification";
import { UserDropdown } from "@/components/ui/user-dropdown";
import { useAuthStore } from "@/stores/auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function DashboardLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const { user: authUser, logout } = useAuthStore();

   const user = authUser || {
      id: "USR-2024-001",
      firstName: "Deji",
      lastName: "Ade",
      username: "dejiade",
      email: "<EMAIL>",
      phoneNumber: "***********",
      country: "Nigeria",
      accountType: "Premium Savings",
   };

   // Mock notification data
   const notifications: Notification[] = [
      {
         id: "notification-1",
         title: "Payment Received",
         message: "You received $2,500.00 from Acme Corp",
         type: "success",
         timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
         read: false,
      },
      {
         id: "notification-2",
         title: "Card Transaction",
         message: "Purchase at Starbucks for $45.99",
         type: "info",
         timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
         read: false,
      },
      {
         id: "notification-3",
         title: "Security Alert",
         message: "New device login detected from Chrome on Windows",
         type: "warning",
         timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
         read: true,
      },
      {
         id: "notification-4",
         title: "Monthly Statement",
         message: "Your December statement is now available",
         type: "info",
         timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
         read: true,
      },
   ];

   // Enhanced user data for dropdown
   const userDropdownData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      accountType: user.accountType,
      verificationStatus: "verified" as const,
   };

   const handleMarkNotificationAsRead = (id: string) => {
      // In a real app, this would update the notification state
      console.log("Mark notification as read:", id);
   };

   const handleMarkAllNotificationsAsRead = () => {
      // In a real app, this would mark all notifications as read
      console.log("Mark all notifications as read");
   };

   const handleViewAllNotifications = () => {
      router.push("/dashboard/notifications");
   };

   const handleUserProfile = () => {
      router.push("/dashboard/profile");
   };

   const handleUserSupport = () => {
      router.push("/dashboard/support");
   };

   const handleLogout = () => {
      logout();
      router.push("/");
   };

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.replace("/login");
      } else if (!isPinVerified) {
         router.replace("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border sticky top-0 z-40">
            <div className="container mx-auto !px-4 !py-4">
               <div className="flex items-center justify-between">
                  {/* Logo */}
                  <BankLogo />

                  {/* Right side - Notifications and User */}
                  <div className="flex items-center gap-4">
                     <NotificationDropdown
                        notifications={notifications}
                        onMarkAsRead={handleMarkNotificationAsRead}
                        onMarkAllAsRead={handleMarkAllNotificationsAsRead}
                        onViewAll={handleViewAllNotifications}
                     />
                     <UserDropdown
                        user={userDropdownData}
                        onProfile={handleUserProfile}
                        onSupport={handleUserSupport}
                        onLogout={handleLogout}
                     />
                  </div>
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main>{children}</main>
      </div>
   );
}
