"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DashboardBreadcrumb } from "@/components/ui/dashboard-breadcrumb";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { Building, CreditCard, Smartphone, Wallet } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function Deposit() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [selectedMethod, setSelectedMethod] = useState("");
   const [amount, setAmount] = useState("");

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const depositMethods = [
      {
         id: "card",
         title: "Debit/Credit Card",
         description: "Instant deposit using your card",
         icon: CreditCard,
         fee: "Free",
         time: "Instant",
      },
      {
         id: "bank",
         title: "Bank Transfer",
         description: "Transfer from another bank account",
         icon: Building,
         fee: "Free",
         time: "1-3 business days",
      },
      {
         id: "mobile",
         title: "Mobile Deposit",
         description: "Deposit checks using your phone",
         icon: Smartphone,
         fee: "Free",
         time: "1-2 business days",
      },
   ];

   const handleDeposit = (e: React.FormEvent) => {
      e.preventDefault();
      if (!selectedMethod || !amount) return;

      // Handle deposit logic
      alert(
         `Deposit of $${amount} via ${selectedMethod} initiated successfully!`
      );
      setAmount("");
      setSelectedMethod("");
   };

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div>
         {/* Breadcrumbs */}
         <DashboardBreadcrumb items={[{ label: "Deposit" }]} />

         {/* Main Content */}
         <div className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-2xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mb-4">
                     <Wallet className="w-8 h-8 text-warning" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     Add Funds
                  </h1>
                  <p className="text-muted-foreground">
                     Choose your preferred method to deposit money into your
                     account.
                  </p>
               </div>

               <div className="bank-card p-6">
                  <form onSubmit={handleDeposit} className="space-y-6">
                     {/* Amount Input */}
                     <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                           Deposit Amount
                        </label>
                        <Input
                           type="number"
                           placeholder="0.00"
                           value={amount}
                           onChange={(e) => setAmount(e.target.value)}
                           className="text-lg"
                           min="1"
                           step="0.01"
                           required
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                           Minimum deposit: $1.00
                        </p>
                     </div>

                     {/* Deposit Methods */}
                     <div>
                        <label className="text-sm font-medium text-foreground mb-4 block">
                           Deposit Method
                        </label>
                        <div className="space-y-3">
                           {depositMethods.map((method) => {
                              const IconComponent = method.icon;
                              return (
                                 <label
                                    key={method.id}
                                    className={`flex items-center gap-4 p-4 rounded-lg border cursor-pointer transition-all ${
                                       selectedMethod === method.id
                                          ? "border-primary bg-primary/5"
                                          : "border-border hover:bg-muted/30"
                                    }`}
                                 >
                                    <input
                                       type="radio"
                                       name="depositMethod"
                                       value={method.id}
                                       checked={selectedMethod === method.id}
                                       onChange={(e) =>
                                          setSelectedMethod(e.target.value)
                                       }
                                       className="sr-only"
                                    />
                                    <div className="p-2 bg-primary/10 rounded-lg">
                                       <IconComponent className="w-5 h-5 text-primary" />
                                    </div>
                                    <div className="flex-1">
                                       <h3 className="font-medium text-foreground">
                                          {method.title}
                                       </h3>
                                       <p className="text-sm text-muted-foreground">
                                          {method.description}
                                       </p>
                                       <div className="flex gap-4 mt-1">
                                          <span className="text-xs text-success">
                                             Fee: {method.fee}
                                          </span>
                                          <span className="text-xs text-muted-foreground">
                                             Time: {method.time}
                                          </span>
                                       </div>
                                    </div>
                                 </label>
                              );
                           })}
                        </div>
                     </div>

                     {/* Submit Button */}
                     <Button
                        type="submit"
                        variant="premium"
                        size="lg"
                        className="w-full"
                        disabled={
                           !selectedMethod || !amount || parseFloat(amount) < 1
                        }
                     >
                        Proceed with Deposit
                     </Button>
                  </form>

                  {/* Security Notice */}
                  <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                     <p className="text-sm text-muted-foreground text-center">
                        🔒 Your deposit is secured with 256-bit SSL encryption
                        and processed through our secure payment gateway.
                     </p>
                  </div>
               </div>
            </motion.div>
         </div>
      </div>
   );
}
