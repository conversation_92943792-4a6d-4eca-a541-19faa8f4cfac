"use client";

import { <PERSON>Logo } from "@/components/bank-logo";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/auth";
import { motion } from "framer-motion";
import { ArrowLeft, Globe, User, DollarSign, MapPin } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function InternationalTransfer() {
   const router = useRouter();
   const { isAuthenticated, isPinVerified } = useAuthStore();
   const [formData, setFormData] = useState({
      recipientName: "",
      recipientAddress: "",
      recipientCountry: "",
      bankName: "",
      swiftCode: "",
      accountNumber: "",
      amount: "",
      currency: "USD",
      purpose: ""
   });

   // Redirect if not authenticated or PIN not verified
   useEffect(() => {
      if (!isAuthenticated) {
         router.push("/login");
      } else if (!isPinVerified) {
         router.push("/pin-verification");
      }
   }, [isAuthenticated, isPinVerified, router]);

   const currencies = [
      { code: "USD", name: "US Dollar" },
      { code: "EUR", name: "Euro" },
      { code: "GBP", name: "British Pound" },
      { code: "CAD", name: "Canadian Dollar" },
      { code: "AUD", name: "Australian Dollar" },
      { code: "JPY", name: "Japanese Yen" }
   ];

   const transferPurposes = [
      "Family Support",
      "Business Payment",
      "Education",
      "Investment",
      "Property Purchase",
      "Other"
   ];

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({
         ...prev,
         [name]: value
      }));
   };

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      // Handle international transfer logic
      alert(`International wire transfer of $${formData.amount} to ${formData.recipientName} in ${formData.recipientCountry} initiated successfully!`);
      setFormData({
         recipientName: "",
         recipientAddress: "",
         recipientCountry: "",
         bankName: "",
         swiftCode: "",
         accountNumber: "",
         amount: "",
         currency: "USD",
         purpose: ""
      });
   };

   const calculateFee = (amount: number) => {
      if (amount < 1000) return 15;
      if (amount < 5000) return 20;
      return 25;
   };

   const fee = formData.amount ? calculateFee(parseFloat(formData.amount)) : 0;
   const total = formData.amount ? parseFloat(formData.amount) + fee : 0;

   if (!isAuthenticated || !isPinVerified) {
      return null;
   }

   return (
      <div className="min-h-screen bg-background">
         {/* Header */}
         <header className="bg-card border-b border-border">
            <div className="container mx-auto px-4 py-4">
               <div className="flex items-center gap-4">
                  <Button
                     variant="ghost"
                     size="sm"
                     onClick={() => router.push("/dashboard")}
                     className="flex items-center gap-2"
                  >
                     <ArrowLeft size={16} />
                     Back to Dashboard
                  </Button>
                  <BankLogo size="sm" />
               </div>
            </div>
         </header>

         {/* Main Content */}
         <main className="container mx-auto px-4 py-8">
            <motion.div
               initial={{ opacity: 0, y: 20 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6 }}
               className="max-w-2xl mx-auto"
            >
               <div className="text-center mb-8">
                  <div className="mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-4">
                     <Globe className="w-8 h-8 text-success" />
                  </div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">
                     International Wire Transfer
                  </h1>
                  <p className="text-muted-foreground">
                     Send money internationally using the SWIFT network. Transfers typically take 1-3 business days.
                  </p>
               </div>

               <div className="bank-card p-6">
                  <form onSubmit={handleSubmit} className="space-y-6">
                     {/* Recipient Information */}
                     <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                           <User className="w-5 h-5" />
                           Recipient Information
                        </h2>
                        
                        <Input
                           label="Recipient Name"
                           name="recipientName"
                           value={formData.recipientName}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's full name"
                           required
                        />

                        <Input
                           label="Recipient Address"
                           name="recipientAddress"
                           value={formData.recipientAddress}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's address"
                           required
                        />

                        <Input
                           label="Country"
                           name="recipientCountry"
                           value={formData.recipientCountry}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's country"
                           required
                        />
                     </div>

                     {/* Bank Information */}
                     <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                           <MapPin className="w-5 h-5" />
                           Bank Information
                        </h2>

                        <Input
                           label="Bank Name"
                           name="bankName"
                           value={formData.bankName}
                           onChange={handleInputChange}
                           placeholder="Enter recipient's bank name"
                           required
                        />

                        <Input
                           label="SWIFT Code"
                           name="swiftCode"
                           value={formData.swiftCode}
                           onChange={handleInputChange}
                           placeholder="Enter bank's SWIFT code"
                           required
                        />

                        <Input
                           label="Account Number/IBAN"
                           name="accountNumber"
                           value={formData.accountNumber}
                           onChange={handleInputChange}
                           placeholder="Enter account number or IBAN"
                           required
                        />
                     </div>

                     {/* Transfer Details */}
                     <div className="space-y-4">
                        <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                           <DollarSign className="w-5 h-5" />
                           Transfer Details
                        </h2>

                        <div className="grid grid-cols-2 gap-4">
                           <Input
                              label="Amount"
                              name="amount"
                              type="number"
                              value={formData.amount}
                              onChange={handleInputChange}
                              placeholder="0.00"
                              min="1"
                              step="0.01"
                              required
                           />

                           <div>
                              <label className="text-sm font-medium text-foreground mb-2 block">
                                 Currency
                              </label>
                              <select
                                 name="currency"
                                 value={formData.currency}
                                 onChange={handleInputChange}
                                 className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                                 required
                              >
                                 {currencies.map((currency) => (
                                    <option key={currency.code} value={currency.code}>
                                       {currency.code} - {currency.name}
                                    </option>
                                 ))}
                              </select>
                           </div>
                        </div>

                        <div>
                           <label className="text-sm font-medium text-foreground mb-2 block">
                              Purpose of Transfer
                           </label>
                           <select
                              name="purpose"
                              value={formData.purpose}
                              onChange={handleInputChange}
                              className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                              required
                           >
                              <option value="">Select purpose</option>
                              {transferPurposes.map((purpose) => (
                                 <option key={purpose} value={purpose}>
                                    {purpose}
                                 </option>
                              ))}
                           </select>
                        </div>
                     </div>

                     {/* Transfer Summary */}
                     {formData.amount && (
                        <div className="bg-muted/30 rounded-lg p-4">
                           <h3 className="font-medium text-foreground mb-3">Transfer Summary</h3>
                           <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">Transfer Amount:</span>
                                 <span className="font-medium">${parseFloat(formData.amount || "0").toFixed(2)}</span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">Transfer Fee:</span>
                                 <span className="font-medium">${fee.toFixed(2)}</span>
                              </div>
                              <div className="flex justify-between border-t border-border pt-2">
                                 <span className="text-muted-foreground">Total:</span>
                                 <span className="font-semibold">${total.toFixed(2)}</span>
                              </div>
                              <div className="flex justify-between">
                                 <span className="text-muted-foreground">Processing Time:</span>
                                 <span className="font-medium">1-3 business days</span>
                              </div>
                           </div>
                        </div>
                     )}

                     {/* Submit Button */}
                     <Button
                        type="submit"
                        variant="premium"
                        size="lg"
                        className="w-full"
                        disabled={!formData.recipientName || !formData.bankName || !formData.swiftCode || !formData.accountNumber || !formData.amount || !formData.purpose || parseFloat(formData.amount) < 1}
                     >
                        Send International Transfer
                     </Button>
                  </form>

                  {/* Security Notice */}
                  <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                     <p className="text-sm text-muted-foreground text-center">
                        🔒 International transfers require additional verification and compliance checks. You may be contacted for additional documentation.
                     </p>
                  </div>
               </div>
            </motion.div>
         </main>
      </div>
   );
}
