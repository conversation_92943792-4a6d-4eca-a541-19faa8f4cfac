"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/bank-logo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
// import { useToast } from "@/hooks/use-toast";
import { useAuthStore } from "@/stores/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
// import { Link, Navigate, useNavigate } from "react-router-dom";
import { z } from "zod";

const loginSchema = z.object({
   email: z.string().email("Please enter a valid email address"),
   password: z.string().min(1, "Password is required"),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function Login() {
   const [showPassword, setShowPassword] = useState(false);
   const [isLoading, setIsLoading] = useState(false);
   const router = useRouter();
   const login = useAuthStore((state) => state.login);
   const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
   // const { toast } = useToast();

   const {
      register,
      handleSubmit,
      formState: { errors },
   } = useForm<LoginForm>({
      resolver: zodResolver(loginSchema),
   });

   if (isAuthenticated) {
      router.push("/pin-verification");
   }

   const onSubmit = async (data: LoginForm) => {
      setIsLoading(true);
      try {
         const success = await login(data.email, data.password);
         if (success) {
            // toast({
            //    title: "Welcome back!",
            //    description: "You have successfully signed in to your account.",
            // });
            router.push("/pin-verification");
         } else {
            // toast({
            //    title: "Invalid credentials",
            //    description:
            //       "Please check your email and password and try again.",
            //    variant: "destructive",
            // });
         }
      } catch (error) {
         console.error(error);
         // toast({
         //    title: "Sign in failed",
         //    description: "An unexpected error occurred. Please try again.",
         //    variant: "destructive",
         // });
      } finally {
         setIsLoading(false);
      }
   };

   return (
      <motion.div
         initial={{ opacity: 0, x: 20 }}
         animate={{ opacity: 1, x: 0 }}
         transition={{ duration: 0.6, delay: 0.2 }}
         className="w-full max-w-md"
      >
         <div className="text-center mb-8 lg:hidden">
            <BankLogo size="md" />
         </div>

         <div className="bank-card p-8">
            <div className="text-center mb-8">
               <h1 className="text-2xl font-bold text-foreground mb-2">
                  Welcome Back
               </h1>
               {/* <p className="text-muted-foreground">
                  Sign in to your Paramount Bank account
               </p> */}
               <p className="text-muted-foreground">
                  Sign in to access your Paramount Bank account{" "}
               </p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
               <Input
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  icon={<Mail size={18} />}
                  error={errors.email?.message}
                  {...register("email")}
               />

               <div className="relative">
                  <Input
                     label="Password"
                     type={showPassword ? "text" : "password"}
                     placeholder="Enter your password"
                     icon={<Lock size={18} />}
                     error={errors.password?.message}
                     {...register("password")}
                  />
                  <button
                     type="button"
                     onClick={() => setShowPassword(!showPassword)}
                     className="absolute right-3 top-9 text-muted-foreground hover:text-foreground transition-colors"
                  >
                     {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
               </div>

               <div className="flex items-center justify-between">
                  <Link
                     href="/forgot-password"
                     className="text-sm text-primary hover:text-primary-light transition-colors"
                  >
                     Forgot password?
                  </Link>
               </div>

               <Button
                  type="submit"
                  variant="premium"
                  size="lg"
                  className="w-full"
                  disabled={isLoading}
               >
                  {isLoading ? "Signing In..." : "Sign In"}
               </Button>
            </form>

            <div className="mt-8 text-center">
               <p className="text-muted-foreground">
                  Don&apos;t have an account?{" "}
                  <Link
                     href="/signup"
                     className="text-primary hover:text-primary-light font-medium transition-colors"
                  >
                     Create an Account
                  </Link>
               </p>
            </div>
         </div>

         <div className="mt-6 text-center">
            <p className="text-xs text-muted-foreground">
               Protected by 256-bit SSL encryption
            </p>
         </div>
      </motion.div>
   );
}
